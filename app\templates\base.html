<!DOCTYPE html>
<html lang="en" dir="ltr" class="lang-ltr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <meta http-equiv="Content-Language" content="en">
    <meta name="format-detection" content="telephone=no">
    <title>{% block title %}{{ _('Device Repair Management System') }}{% endblock %}</title>

    <!-- PWA Meta Tags -->
    <meta name="theme-color" content="#007bff">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="Device Repair">
    <meta name="description" content="Professional device repair management system">

    <!-- Favicon and PWA Icons -->
    <link rel="icon" type="image/svg+xml" href="{{ url_for('static', filename='img/favicon.svg') }}">
    <link rel="alternate icon" href="{{ url_for('static', filename='img/favicon.svg') }}">
    <link rel="apple-touch-icon" href="{{ url_for('static', filename='img/icon-192.png') }}">
    <link rel="manifest" href="{{ url_for('static', filename='manifest.json') }}">

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <!-- Animate.css -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">
    <!-- Socket.IO for real-time updates -->
    <script src="https://cdn.socket.io/4.7.2/socket.io.min.js"></script>
    <!-- Custom CSS -->
    <style>
        :root {
            --primary-color: #1a73e8;
            --secondary-color: #f8f9fa;
            --accent-color: #ff6b6b;
            --text-color: #333;
            --light-text: #6c757d;
            --bg-color: #f5f7fa;
            --card-bg: #ffffff;
            --dark-bg-color: #121212;
            --dark-card-bg: #1e1e1e;
            --dark-text-color: #e0e0e0;
            --dark-light-text: #aaaaaa;
        }

        body {
            font-family: 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', sans-serif;
            background-color: var(--bg-color);
            color: var(--text-color);
            transition: background-color 0.3s ease, color 0.3s ease;
            font-variant-numeric: lining-nums;
        }

        /* Force English numerals globally */
        * {
            font-variant-numeric: lining-nums;
        }

        input[type="number"], .number-display, .cost-display, .ticket-id, .price-text {
            direction: ltr !important;
            unicode-bidi: embed;
            font-family: 'Segoe UI', 'Arial', sans-serif !important;
            font-variant-numeric: lining-nums;
        }

        body.dark-mode {
            background-color: var(--dark-bg-color);
            color: var(--dark-text-color);
        }

        .navbar {
            box-shadow: 0 2px 15px rgba(0, 0, 0, 0.1);
        }

        .navbar-brand {
            font-weight: 600;
            letter-spacing: 0.5px;
        }

        .card {
            border: none;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
            transition: transform 0.3s, box-shadow 0.3s, background-color 0.3s ease;
            background-color: var(--card-bg);
        }

        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
        }

        .card-header {
            border-radius: 10px 10px 0 0 !important;
            font-weight: 600;
        }

        body.dark-mode .card {
            background-color: var(--dark-card-bg);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }

        .btn {
            border-radius: 6px;
            font-weight: 500;
            padding: 8px 20px;
            transition: all 0.3s;
        }

        .btn-primary {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }

        .btn-primary:hover {
            background-color: #0d6efd;
            border-color: #0d6efd;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }

        .sidebar {
            min-height: calc(100vh - 56px);
            background-color: white;
            padding-top: 1rem;
            box-shadow: 1px 0 10px rgba(0, 0, 0, 0.05);
            border-right: 1px solid #eee;
        }

        .sidebar .nav-link {
            color: var(--text-color);
            padding: 0.8rem 1.2rem;
            margin-bottom: 0.3rem;
            border-radius: 8px;
            transition: all 0.3s;
        }

        .sidebar .nav-link:hover {
            background-color: rgba(13, 110, 253, 0.1);
            color: var(--primary-color);
            transform: translateX(5px);
        }

        .sidebar .nav-link.active {
            background-color: var(--primary-color);
            color: white;
            box-shadow: 0 4px 8px rgba(13, 110, 253, 0.3);
        }

        .main-content {
            padding: 2rem;
            animation: fadeIn 0.5s;
        }

        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 8px;
            box-shadow: 0 0 5px rgba(0, 0, 0, 0.2);
        }

        .status-received { background-color: #dc3545; }
        .status-in_progress { background-color: #fd7e14; }
        .status-repaired { background-color: #0dcaf0; }
        .status-delivered { background-color: #198754; }

        .alert {
            border-radius: 8px;
            border: none;
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
        }

        .form-control, .form-select {
            border-radius: 6px;
            padding: 10px 15px;
            border: 1px solid #dee2e6;
            transition: all 0.3s;
        }

        .form-control:focus, .form-select:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        /* Custom animations for page transitions */
        .page-enter {
            animation: fadeInRight 0.5s forwards;
        }

        @keyframes fadeInRight {
            from {
                opacity: 0;
                transform: translateX(20px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        .dashboard-card {
            border-left: 4px solid var(--primary-color);
            background: linear-gradient(to right, rgba(13, 110, 253, 0.05), white);
        }

        .table {
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
        }

        .page-title {
            font-weight: 600;
            margin-bottom: 1.5rem;
            color: var(--primary-color);
        }

        /* Dark mode styles */
        body.dark-mode .navbar {
            background-color: var(--dark-card-bg) !important;
            box-shadow: 0 2px 15px rgba(0, 0, 0, 0.3);
        }

        body.dark-mode .navbar-brand,
        body.dark-mode .nav-link,
        body.dark-mode .navbar-text {
            color: var(--dark-text-color) !important;
        }

        body.dark-mode .dropdown-menu {
            background-color: var(--dark-card-bg);
            border-color: #333;
        }

        body.dark-mode .dropdown-item {
            color: var(--dark-text-color);
        }

        body.dark-mode .dropdown-item:hover {
            background-color: #333;
        }

        body.dark-mode .sidebar {
            background-color: var(--dark-bg-color);
        }

        body.dark-mode .form-control,
        body.dark-mode .form-select {
            background-color: #2a2a2a;
            border-color: #444;
            color: var(--dark-text-color);
        }

        /* Dark mode toggle */
        .dark-mode-toggle {
            position: fixed;
            bottom: 20px;
            right: 20px;
            background-color: var(--primary-color);
            color: white;
            border: none;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            font-size: 1.2rem;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
            z-index: 1000;
            transition: all 0.3s;
            cursor: pointer;
        }

        .dark-mode-toggle:hover {
            transform: rotate(45deg);
        }


    </style>

    {% block additional_styles %}{% endblock %}
</head>
<body>
    <!-- Navigation Bar -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container-fluid">
            <a class="navbar-brand animate__animated animate__fadeIn" href="{{ url_for('main.index') }}">
                <i class="bi bi-tools me-2"></i>{{ _('Device Repair Management System') }}
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    {% if current_user.is_authenticated %}
                    <!-- Notification Status Indicator -->
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('auth.profile') }}" id="notificationStatus" title="Notification Status">
                            <i class="bi bi-bell" id="notificationIcon"></i>
                            <span class="badge bg-secondary rounded-pill ms-1" id="notificationBadge" style="font-size: 0.7em;">?</span>
                        </a>
                    </li>

                    <!-- Admin Notification Settings -->
                    {% if current_user.is_admin() %}
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('notifications.settings') }}" title="Notification Settings">
                            <i class="bi bi-gear"></i>
                        </a>
                    </li>
                    {% endif %}

                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="bi bi-person-circle"></i> {{ current_user.username }}
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="{{ url_for('auth.profile') }}">
                                <i class="bi bi-person me-2"></i>{{ _('Profile') }}
                            </a></li>
                            <li><a class="dropdown-item" href="{{ url_for('auth.profile') }}">
                                <i class="bi bi-bell me-2"></i>{{ _('Notification Preferences') }}
                            </a></li>
                            {% if current_user.is_admin() %}
                            <li><a class="dropdown-item" href="{{ url_for('business_settings.settings') }}">
                                <i class="bi bi-building me-2"></i>Business Settings
                            </a></li>
                            <li><a class="dropdown-item" href="{{ url_for('users.index') }}">
                                <i class="bi bi-people me-2"></i>Manage Users
                            </a></li>
                            <li><a class="dropdown-item" href="{{ url_for('notifications.settings') }}">
                                <i class="bi bi-gear me-2"></i>{{ _('Notification Settings') }}
                            </a></li>
                            {% endif %}
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="{{ url_for('auth.logout') }}">
                                <i class="bi bi-box-arrow-right me-2"></i>{{ _('Logout') }}
                            </a></li>
                        </ul>
                    </li>
                    {% else %}
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('auth.login') }}">
                            <i class="bi bi-box-arrow-in-right me-1"></i> {{ _('Login') }}
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('auth.register') }}">
                            <i class="bi bi-person-plus me-1"></i> {{ _('Register') }}
                        </a>
                    </li>
                    {% endif %}


                </ul>
            </div>
        </div>
    </nav>

    <div class="container-fluid">
        <div class="row">
            {% if current_user.is_authenticated %}
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 d-md-block sidebar collapse">
                <ul class="nav flex-column">
                    <!-- Dashboard - Available to everyone -->
                    <li class="nav-item">
                        <a class="nav-link {% if request.endpoint == 'dashboard.index' %}active animate__animated animate__pulse{% endif %}"
                           href="{{ url_for('dashboard.index') }}">
                            <i class="bi bi-speedometer2 me-2"></i> {{ _('Dashboard') }}
                        </a>
                    </li>

                    <!-- Devices - Available to everyone -->
                    <li class="nav-item">
                        <a class="nav-link {% if request.endpoint.startswith('device.') %}active animate__animated animate__pulse{% endif %}"
                           href="{{ url_for('device.index') }}">
                            <i class="bi bi-phone me-2"></i> {{ _('Devices') }}
                        </a>
                    </li>

                    <!-- Spare Parts Inventory - Available to everyone -->
                    <li class="nav-item">
                        <a class="nav-link {% if request.endpoint.startswith('spare_parts.') %}active animate__animated animate__pulse{% endif %}"
                           href="{{ url_for('spare_parts.index') }}">
                            <i class="bi bi-box-seam me-2"></i> {{ _('Spare Parts') }}
                        </a>
                    </li>

                    <!-- Technicians - Available to admin only -->
                    {% if current_user.can_manage_users() %}
                    <li class="nav-item">
                        <a class="nav-link {% if request.endpoint.startswith('technician.') %}active animate__animated animate__pulse{% endif %}"
                           href="{{ url_for('technician.index') }}">
                            <i class="bi bi-person-badge me-2"></i> {{ _('Technicians') }}
                        </a>
                    </li>
                    {% endif %}

                    <!-- Accounting - Available to admin only -->
                    {% if current_user.can_view_financial_reports() %}
                    <li class="nav-item">
                        <a class="nav-link {% if request.endpoint.startswith('accounting.') %}active animate__animated animate__pulse{% endif %}"
                           href="{{ url_for('accounting.index') }}">
                            <i class="bi bi-currency-dollar me-2"></i> {{ _('Accounting') }}
                        </a>
                    </li>
                    {% endif %}

                    <!-- User Management - Available to admin only -->
                    {% if current_user.can_manage_users() %}
                    <li class="nav-item">
                        <a class="nav-link {% if request.endpoint.startswith('users.') %}active animate__animated animate__pulse{% endif %}"
                           href="{{ url_for('users.index') }}">
                            <i class="bi bi-people me-2"></i> {{ _('Users') }}
                        </a>
                    </li>
                    {% endif %}

                    <!-- Authorized Staff - Available to admin only -->
                    {% if current_user.can_manage_users() %}
                    <li class="nav-item">
                        <a class="nav-link {% if request.endpoint.startswith('staff.') %}active animate__animated animate__pulse{% endif %}"
                           href="{{ url_for('staff.index') }}">
                            <i class="bi bi-person-vcard me-2"></i> {{ _('Authorized Staff') }}
                        </a>
                    </li>
                    {% endif %}

                    <!-- System Status navigation removed - admin features isolated to standalone tool -->

                    <!-- Admin navigation links removed - now handled by separate standalone admin tool -->

                    <!-- WhatsApp Test removed - causing BuildError -->

                    <!-- Settings - Available to admin only -->
                    {% if current_user.can_manage_settings() %}
                    <li class="nav-item">
                        <a class="nav-link {% if request.endpoint == 'dashboard.settings' %}active animate__animated animate__pulse{% endif %}"
                           href="{{ url_for('dashboard.settings') }}">
                            <i class="bi bi-sliders me-2"></i> {{ _('Settings') }}
                        </a>
                    </li>
                    {% endif %}
                </ul>
            </div>

            <!-- Main Content -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 main-content page-enter">
            {% else %}
            <!-- Full-width content for unauthenticated users -->
            <main class="col-12 page-enter">
            {% endif %}
                {% block content %}{% endblock %}
            </main>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Dark Mode Toggle Button -->
    <button class="btn dark-mode-toggle" id="darkModeToggle">
        <i class="bi bi-moon"></i>
    </button>

    <!-- Common JavaScript -->
    <script>
        // CSRF Token Setup for AJAX requests
        document.addEventListener('DOMContentLoaded', function() {
            // Get CSRF token from meta tag
            const csrfToken = document.querySelector('meta[name="csrf-token"]');
            if (csrfToken) {
                // Set up CSRF token for all AJAX requests
                const token = csrfToken.getAttribute('content');

                // jQuery AJAX setup (if jQuery is available)
                if (typeof $ !== 'undefined') {
                    $.ajaxSetup({
                        beforeSend: function(xhr, settings) {
                            if (!/^(GET|HEAD|OPTIONS|TRACE)$/i.test(settings.type) && !this.crossDomain) {
                                xhr.setRequestHeader("X-CSRFToken", token);
                            }
                        }
                    });
                }

                // Fetch API setup
                const originalFetch = window.fetch;
                window.fetch = function(url, options = {}) {
                    if (options.method && !['GET', 'HEAD', 'OPTIONS', 'TRACE'].includes(options.method.toUpperCase())) {
                        options.headers = options.headers || {};
                        options.headers['X-CSRFToken'] = token;
                    }
                    return originalFetch(url, options);
                };

                // Add CSRF token to all forms
                document.querySelectorAll('form').forEach(form => {
                    if (!form.querySelector('input[name="csrf_token"]')) {
                        const csrfInput = document.createElement('input');
                        csrfInput.type = 'hidden';
                        csrfInput.name = 'csrf_token';
                        csrfInput.value = token;
                        form.appendChild(csrfInput);
                    }
                });
            }
        });

        // Function to apply dark mode
        function applyDarkMode(isDarkMode) {
            if (isDarkMode) {
                document.body.classList.add('dark-mode');
                const icon = document.querySelector('#darkModeToggle i');
                if (icon && icon.classList.contains('bi-moon')) {
                    icon.classList.replace('bi-moon', 'bi-sun');
                }
                localStorage.setItem('darkMode', 'enabled');
            } else {
                document.body.classList.remove('dark-mode');
                const icon = document.querySelector('#darkModeToggle i');
                if (icon && icon.classList.contains('bi-sun')) {
                    icon.classList.replace('bi-sun', 'bi-moon');
                }
                localStorage.setItem('darkMode', 'disabled');
            }
        }

        // Dark mode toggle functionality with localStorage persistence
        document.addEventListener('DOMContentLoaded', function() {
            const darkModeToggle = document.getElementById('darkModeToggle');
            if (darkModeToggle) {
                darkModeToggle.addEventListener('click', function() {
                    const isDarkMode = !document.body.classList.contains('dark-mode');
                    applyDarkMode(isDarkMode);
                });
            }

            // Check for saved dark mode preference and apply it
            const darkModePreference = localStorage.getItem('darkMode');
            const prefersDarkMode = window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches;

            // Enable dark mode if previously enabled or if user prefers dark mode
            if (darkModePreference === 'enabled' || (darkModePreference === null && prefersDarkMode)) {
                applyDarkMode(true);
            } else if (darkModePreference === 'disabled') {
                applyDarkMode(false);
            }
        });

        // Notification status checker
        function updateNotificationStatus() {
            {% if current_user.is_authenticated %}
            fetch('/notifications/status')
                .then(response => response.json())
                .then(data => {
                    const icon = document.getElementById('notificationIcon');
                    const badge = document.getElementById('notificationBadge');

                    if (data.success && data.status) {
                        const status = data.status;

                        // Update icon based on system status
                        if (status.enabled) {
                            icon.className = 'bi bi-bell-fill text-success';

                            // Count active platforms (Telegram only)
                            let activePlatforms = 0;
                            if (status.telegram_enabled && status.telegram_status === 'connected') activePlatforms++;

                            badge.textContent = activePlatforms;
                            badge.className = activePlatforms > 0 ? 'badge bg-success rounded-pill ms-1' : 'badge bg-warning rounded-pill ms-1';
                        } else {
                            icon.className = 'bi bi-bell-slash text-muted';
                            badge.textContent = '0';
                            badge.className = 'badge bg-secondary rounded-pill ms-1';
                        }
                    } else {
                        // Error state
                        icon.className = 'bi bi-bell text-warning';
                        badge.textContent = '!';
                        badge.className = 'badge bg-danger rounded-pill ms-1';
                    }
                })
                .catch(error => {
                    console.log('Notification status check failed:', error);
                    const icon = document.getElementById('notificationIcon');
                    const badge = document.getElementById('notificationBadge');
                    icon.className = 'bi bi-bell text-muted';
                    badge.textContent = '?';
                    badge.className = 'badge bg-secondary rounded-pill ms-1';
                });
            {% endif %}
        }

        // Add animation classes to elements when they come into view
        document.addEventListener('DOMContentLoaded', function() {
            // Update notification status on page load
            updateNotificationStatus();

            // Update notification status every 30 seconds
            setInterval(updateNotificationStatus, 30000);

            // Add animation to cards
            document.querySelectorAll('.card').forEach(card => {
                card.classList.add('animate__animated', 'animate__fadeIn');
            });

            // Add animation to alerts
            document.querySelectorAll('.alert').forEach(alert => {
                alert.classList.add('animate__animated', 'animate__fadeIn');
            });
        });
    </script>

    {% block scripts %}{% endblock %}
</body>
</html>