2025-05-30 15:09:30,489 - device_repair_system - INFO - Error handling system initialized
2025-05-30 15:09:33,700 - device_repair_system - INFO - Error handling system initialized
2025-05-30 15:27:10,407 - device_repair_system - INFO - Error handling system initialized
2025-05-30 15:29:15,725 - device_repair_system - INFO - Error handling system initialized
2025-05-30 15:30:00,627 - device_repair_system - INFO - Error handling system initialized
2025-05-30 15:30:13,519 - device_repair_system - INFO - Error handling system initialized
2025-05-30 15:30:35,777 - device_repair_system - INFO - Error handling system initialized
2025-05-30 15:30:39,267 - device_repair_system - INFO - Error handling system initialized
2025-05-30 15:31:46,657 - device_repair_system - INFO - Error handling system initialized
2025-05-30 15:53:13,929 - device_repair_system - INFO - Error handling system initialized
2025-05-30 15:53:33,033 - device_repair_system - INFO - Error handling system initialized
2025-05-30 15:53:35,295 - device_repair_system - INFO - Error handling system initialized
2025-05-30 15:55:08,728 - device_repair_system - INFO - Error handling system initialized
2025-05-30 15:55:35,965 - device_repair_system - INFO - Error handling system initialized
2025-05-30 15:56:29,711 - device_repair_system - INFO - Error handling system initialized
2025-05-30 15:56:32,135 - device_repair_system - INFO - Error handling system initialized
2025-05-30 16:00:07,921 - device_repair_system - INFO - Error handling system initialized
2025-05-30 16:00:36,555 - device_repair_system - INFO - Error handling system initialized
2025-05-30 16:00:55,889 - device_repair_system - INFO - Error handling system initialized
2025-05-30 16:01:20,645 - device_repair_system - INFO - Error handling system initialized
2025-05-30 16:02:37,456 - device_repair_system - INFO - Error handling system initialized
2025-05-30 16:02:39,787 - device_repair_system - INFO - Error handling system initialized
2025-05-30 16:03:26,145 - device_repair_system - ERROR - Server Error: {'timestamp': '2025-05-30T16:03:26.055267', 'status_code': 500, 'error_type': 'TemplateNotFound', 'error_message': 'whatsapp_test/instructions.html', 'url': 'http://127.0.0.1:5000/whatsapp-test/instructions', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous', 'traceback': 'Traceback (most recent call last):\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1484, in full_dispatch_request\n    rv = self.dispatch_request()\n         ^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1469, in dispatch_request\n    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask_login\\utils.py", line 290, in decorated_view\n    return current_app.ensure_sync(func)(*args, **kwargs)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "E:\\device repair manaement system - Copy\\app\\routes\\whatsapp_single_window_test.py", line 190, in instructions\n    return render_template(\'whatsapp_test/instructions.html\')\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\templating.py", line 150, in render_template\n    template = app.jinja_env.get_or_select_template(template_name_or_list)\n               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\environment.py", line 1087, in get_or_select_template\n    return self.get_template(template_name_or_list, parent, globals)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\environment.py", line 1016, in get_template\n    return self._load_template(name, globals)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\environment.py", line 975, in _load_template\n    template = self.loader.load(self, name, self.make_globals(globals))\n               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\loaders.py", line 126, in load\n    source, filename, uptodate = self.get_source(environment, name)\n                                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\templating.py", line 64, in get_source\n    return self._get_source_fast(environment, template)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\templating.py", line 98, in _get_source_fast\n    raise TemplateNotFound(template)\njinja2.exceptions.TemplateNotFound: whatsapp_test/instructions.html\n'}
2025-05-30 16:04:36,396 - device_repair_system - ERROR - Server Error: {'timestamp': '2025-05-30T16:04:36.394979', 'status_code': 500, 'error_type': 'TemplateNotFound', 'error_message': 'whatsapp_test/instructions.html', 'url': 'http://127.0.0.1:5000/whatsapp-test/instructions', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous', 'traceback': 'Traceback (most recent call last):\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1484, in full_dispatch_request\n    rv = self.dispatch_request()\n         ^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1469, in dispatch_request\n    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask_login\\utils.py", line 290, in decorated_view\n    return current_app.ensure_sync(func)(*args, **kwargs)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "E:\\device repair manaement system - Copy\\app\\routes\\whatsapp_single_window_test.py", line 190, in instructions\n    return render_template(\'whatsapp_test/instructions.html\')\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\templating.py", line 150, in render_template\n    template = app.jinja_env.get_or_select_template(template_name_or_list)\n               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\environment.py", line 1087, in get_or_select_template\n    return self.get_template(template_name_or_list, parent, globals)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\environment.py", line 1016, in get_template\n    return self._load_template(name, globals)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\environment.py", line 975, in _load_template\n    template = self.loader.load(self, name, self.make_globals(globals))\n               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\loaders.py", line 126, in load\n    source, filename, uptodate = self.get_source(environment, name)\n                                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\templating.py", line 64, in get_source\n    return self._get_source_fast(environment, template)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\templating.py", line 98, in _get_source_fast\n    raise TemplateNotFound(template)\njinja2.exceptions.TemplateNotFound: whatsapp_test/instructions.html\n'}
2025-05-30 16:04:55,265 - device_repair_system - ERROR - Server Error: {'timestamp': '2025-05-30T16:04:55.264494', 'status_code': 500, 'error_type': 'TemplateNotFound', 'error_message': 'whatsapp_test/instructions.html', 'url': 'http://127.0.0.1:5000/whatsapp-test/instructions', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous', 'traceback': 'Traceback (most recent call last):\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1484, in full_dispatch_request\n    rv = self.dispatch_request()\n         ^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1469, in dispatch_request\n    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask_login\\utils.py", line 290, in decorated_view\n    return current_app.ensure_sync(func)(*args, **kwargs)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "E:\\device repair manaement system - Copy\\app\\routes\\whatsapp_single_window_test.py", line 190, in instructions\n    return render_template(\'whatsapp_test/instructions.html\')\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\templating.py", line 150, in render_template\n    template = app.jinja_env.get_or_select_template(template_name_or_list)\n               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\environment.py", line 1087, in get_or_select_template\n    return self.get_template(template_name_or_list, parent, globals)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\environment.py", line 1016, in get_template\n    return self._load_template(name, globals)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\environment.py", line 975, in _load_template\n    template = self.loader.load(self, name, self.make_globals(globals))\n               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\loaders.py", line 126, in load\n    source, filename, uptodate = self.get_source(environment, name)\n                                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\templating.py", line 64, in get_source\n    return self._get_source_fast(environment, template)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\templating.py", line 98, in _get_source_fast\n    raise TemplateNotFound(template)\njinja2.exceptions.TemplateNotFound: whatsapp_test/instructions.html\n'}
2025-05-30 16:05:10,908 - device_repair_system - ERROR - Server Error: {'timestamp': '2025-05-30T16:05:10.907547', 'status_code': 500, 'error_type': 'TemplateNotFound', 'error_message': 'whatsapp_test/instructions.html', 'url': 'http://127.0.0.1:5000/whatsapp-test/instructions', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous', 'traceback': 'Traceback (most recent call last):\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1484, in full_dispatch_request\n    rv = self.dispatch_request()\n         ^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1469, in dispatch_request\n    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask_login\\utils.py", line 290, in decorated_view\n    return current_app.ensure_sync(func)(*args, **kwargs)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "E:\\device repair manaement system - Copy\\app\\routes\\whatsapp_single_window_test.py", line 190, in instructions\n    return render_template(\'whatsapp_test/instructions.html\')\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\templating.py", line 150, in render_template\n    template = app.jinja_env.get_or_select_template(template_name_or_list)\n               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\environment.py", line 1087, in get_or_select_template\n    return self.get_template(template_name_or_list, parent, globals)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\environment.py", line 1016, in get_template\n    return self._load_template(name, globals)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\environment.py", line 975, in _load_template\n    template = self.loader.load(self, name, self.make_globals(globals))\n               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\loaders.py", line 126, in load\n    source, filename, uptodate = self.get_source(environment, name)\n                                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\templating.py", line 64, in get_source\n    return self._get_source_fast(environment, template)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\templating.py", line 98, in _get_source_fast\n    raise TemplateNotFound(template)\njinja2.exceptions.TemplateNotFound: whatsapp_test/instructions.html\n'}
2025-05-30 16:19:04,857 - device_repair_system - INFO - Error handling system initialized
2025-05-30 16:20:37,719 - device_repair_system - INFO - Error handling system initialized
2025-05-30 16:21:58,273 - device_repair_system - INFO - Error handling system initialized
2025-05-30 16:22:13,627 - device_repair_system - INFO - Error handling system initialized
2025-05-30 16:23:47,127 - device_repair_system - INFO - Error handling system initialized
2025-05-30 16:23:49,606 - device_repair_system - INFO - Error handling system initialized
2025-05-30 16:28:54,064 - device_repair_system - INFO - Error handling system initialized
2025-05-30 16:39:18,606 - device_repair_system - INFO - Error handling system initialized
2025-05-30 16:41:27,081 - device_repair_system - INFO - Error handling system initialized
2025-05-30 16:41:29,566 - device_repair_system - INFO - Error handling system initialized
2025-05-30 16:45:50,520 - device_repair_system - WARNING - Client Error: {'timestamp': '2025-05-30T16:45:50.520037', 'status_code': 404, 'error_type': 'NotFound', 'error_message': '404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.', 'url': 'http://127.0.0.1:5000/favicon.ico', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous'}
2025-05-30 16:46:06,940 - device_repair_system - ERROR - Server Error: {'timestamp': '2025-05-30T16:46:06.905032', 'status_code': 500, 'error_type': 'BuildError', 'error_message': "Could not build url for endpoint 'whatsapp_test.index'. Did you mean 'users.index' instead?", 'url': 'http://127.0.0.1:5000/dashboard/', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous', 'traceback': 'Traceback (most recent call last):\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1484, in full_dispatch_request\n    rv = self.dispatch_request()\n         ^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1469, in dispatch_request\n    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask_login\\utils.py", line 290, in decorated_view\n    return current_app.ensure_sync(func)(*args, **kwargs)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "E:\\device repair manaement system - Copy\\app\\routes\\dashboard.py", line 50, in index\n    return render_template(\n           ^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\templating.py", line 151, in render_template\n    return _render(app, template, context)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\templating.py", line 132, in _render\n    rv = template.render(context)\n         ^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\environment.py", line 1295, in render\n    self.environment.handle_exception()\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\environment.py", line 942, in handle_exception\n    raise rewrite_traceback_stack(source=source)\n  File "E:\\device repair manaement system - Copy\\app\\templates\\dashboard\\index.html", line 1, in top-level template code\n    {% extends \'base.html\' %}\n  File "E:\\device repair manaement system - Copy\\app\\templates\\base.html", line 422, in top-level template code\n    href="{{ url_for(\'whatsapp_test.index\') }}">\n    ^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1697, in url_for\n    return self.handle_url_build_error(error, endpoint, values)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1686, in url_for\n    rv = url_adapter.build(  # type: ignore[union-attr]\n         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\werkzeug\\routing\\map.py", line 950, in build\n    raise BuildError(endpoint, values, method, self)\nwerkzeug.routing.exceptions.BuildError: Could not build url for endpoint \'whatsapp_test.index\'. Did you mean \'users.index\' instead?\n'}
2025-05-30 16:48:37,637 - device_repair_system - ERROR - Server Error: {'timestamp': '2025-05-30T16:48:37.631118', 'status_code': 500, 'error_type': 'BuildError', 'error_message': "Could not build url for endpoint 'whatsapp_test.index'. Did you mean 'users.index' instead?", 'url': 'http://127.0.0.1:5000/dashboard/', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous', 'traceback': 'Traceback (most recent call last):\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1484, in full_dispatch_request\n    rv = self.dispatch_request()\n         ^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1469, in dispatch_request\n    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask_login\\utils.py", line 290, in decorated_view\n    return current_app.ensure_sync(func)(*args, **kwargs)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "E:\\device repair manaement system - Copy\\app\\routes\\dashboard.py", line 50, in index\n    return render_template(\n           ^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\templating.py", line 151, in render_template\n    return _render(app, template, context)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\templating.py", line 132, in _render\n    rv = template.render(context)\n         ^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\environment.py", line 1295, in render\n    self.environment.handle_exception()\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\environment.py", line 942, in handle_exception\n    raise rewrite_traceback_stack(source=source)\n  File "E:\\device repair manaement system - Copy\\app\\templates\\dashboard\\index.html", line 1, in top-level template code\n    {% extends \'base.html\' %}\n  File "E:\\device repair manaement system - Copy\\app\\templates\\base.html", line 422, in top-level template code\n    href="{{ url_for(\'whatsapp_test.index\') }}">\n    ^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1697, in url_for\n    return self.handle_url_build_error(error, endpoint, values)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1686, in url_for\n    rv = url_adapter.build(  # type: ignore[union-attr]\n         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\werkzeug\\routing\\map.py", line 950, in build\n    raise BuildError(endpoint, values, method, self)\nwerkzeug.routing.exceptions.BuildError: Could not build url for endpoint \'whatsapp_test.index\'. Did you mean \'users.index\' instead?\n'}
2025-05-30 16:50:10,696 - device_repair_system - INFO - Error handling system initialized
2025-05-30 16:50:13,138 - device_repair_system - INFO - Error handling system initialized
2025-05-30 16:55:43,988 - device_repair_system - INFO - Error handling system initialized
2025-05-30 16:55:46,252 - device_repair_system - INFO - Error handling system initialized
2025-05-30 17:15:19,347 - device_repair_system - INFO - Error handling system initialized
2025-05-30 17:15:21,635 - device_repair_system - INFO - Error handling system initialized
2025-05-30 18:33:12,305 - device_repair_system - INFO - Error handling system initialized
2025-05-30 18:33:14,839 - device_repair_system - INFO - Error handling system initialized
2025-05-30 18:35:20,422 - device_repair_system - INFO - Error handling system initialized
2025-05-30 18:35:22,852 - device_repair_system - INFO - Error handling system initialized
2025-05-30 18:38:47,070 - device_repair_system - INFO - Error handling system initialized
2025-05-30 18:39:05,327 - device_repair_system - INFO - Error handling system initialized
2025-05-30 18:39:33,881 - device_repair_system - INFO - Error handling system initialized
2025-05-30 18:39:47,461 - device_repair_system - INFO - Error handling system initialized
2025-05-30 18:40:02,551 - device_repair_system - INFO - Error handling system initialized
2025-05-30 18:40:19,713 - device_repair_system - INFO - Error handling system initialized
2025-05-30 18:40:52,427 - device_repair_system - INFO - Error handling system initialized
2025-05-30 18:41:03,355 - device_repair_system - INFO - Error handling system initialized
2025-05-30 18:41:26,790 - device_repair_system - INFO - Error handling system initialized
2025-05-30 18:46:10,396 - device_repair_system - INFO - Error handling system initialized
2025-05-30 18:46:13,320 - device_repair_system - INFO - Error handling system initialized
2025-05-31 11:42:00,826 - device_repair_system - INFO - Error handling system initialized
2025-05-31 11:42:35,694 - device_repair_system - INFO - Error handling system initialized
2025-05-31 11:47:20,494 - device_repair_system - INFO - Error handling system initialized
2025-05-31 11:47:23,574 - device_repair_system - INFO - Error handling system initialized
2025-05-31 11:48:31,999 - device_repair_system - ERROR - Server Error: {'timestamp': '2025-05-31T11:48:31.872920', 'status_code': 500, 'error_type': 'UndefinedError', 'error_message': "'get_locale' is undefined", 'url': 'http://127.0.0.1:5000/activate', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous', 'traceback': 'Traceback (most recent call last):\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1484, in full_dispatch_request\n    rv = self.dispatch_request()\n         ^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1469, in dispatch_request\n    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "E:\\device repair manaement system - Copy\\app\\routes\\main.py", line 29, in activate_form\n    return render_template(\'activate.html\')\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\templating.py", line 151, in render_template\n    return _render(app, template, context)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\templating.py", line 132, in _render\n    rv = template.render(context)\n         ^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\environment.py", line 1295, in render\n    self.environment.handle_exception()\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\environment.py", line 942, in handle_exception\n    raise rewrite_traceback_stack(source=source)\n  File "E:\\device repair manaement system - Copy\\app\\templates\\activate.html", line 1, in top-level template code\n    {% extends "base.html" %}\n  File "E:\\device repair manaement system - Copy\\app\\templates\\base.html", line 2, in top-level template code\n    {% set current_language = get_locale() %}\n    ^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\utils.py", line 92, in from_obj\n    if hasattr(obj, "jinja_pass_arg"):\n       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\njinja2.exceptions.UndefinedError: \'get_locale\' is undefined\n'}
2025-05-31 11:48:32,465 - device_repair_system - WARNING - Client Error: {'timestamp': '2025-05-31T11:48:32.465337', 'status_code': 404, 'error_type': 'NotFound', 'error_message': '404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.', 'url': 'http://127.0.0.1:5000/favicon.ico', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous'}
2025-05-31 11:48:53,990 - device_repair_system - ERROR - Server Error: {'timestamp': '2025-05-31T11:48:53.988166', 'status_code': 500, 'error_type': 'UndefinedError', 'error_message': "'get_locale' is undefined", 'url': 'http://127.0.0.1:5000/activate', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous', 'traceback': 'Traceback (most recent call last):\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1484, in full_dispatch_request\n    rv = self.dispatch_request()\n         ^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1469, in dispatch_request\n    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "E:\\device repair manaement system - Copy\\app\\routes\\main.py", line 29, in activate_form\n    return render_template(\'activate.html\')\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\templating.py", line 151, in render_template\n    return _render(app, template, context)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\templating.py", line 132, in _render\n    rv = template.render(context)\n         ^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\environment.py", line 1295, in render\n    self.environment.handle_exception()\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\environment.py", line 942, in handle_exception\n    raise rewrite_traceback_stack(source=source)\n  File "E:\\device repair manaement system - Copy\\app\\templates\\activate.html", line 1, in top-level template code\n    {% extends "base.html" %}\n  File "E:\\device repair manaement system - Copy\\app\\templates\\base.html", line 2, in top-level template code\n    {% set current_language = get_locale() %}\n    ^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\utils.py", line 92, in from_obj\n    if hasattr(obj, "jinja_pass_arg"):\n       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\njinja2.exceptions.UndefinedError: \'get_locale\' is undefined\n'}
2025-05-31 11:48:59,826 - device_repair_system - ERROR - Server Error: {'timestamp': '2025-05-31T11:48:59.825535', 'status_code': 500, 'error_type': 'UndefinedError', 'error_message': "'get_locale' is undefined", 'url': 'http://127.0.0.1:5000/activate', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous', 'traceback': 'Traceback (most recent call last):\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1484, in full_dispatch_request\n    rv = self.dispatch_request()\n         ^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1469, in dispatch_request\n    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "E:\\device repair manaement system - Copy\\app\\routes\\main.py", line 29, in activate_form\n    return render_template(\'activate.html\')\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\templating.py", line 151, in render_template\n    return _render(app, template, context)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\templating.py", line 132, in _render\n    rv = template.render(context)\n         ^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\environment.py", line 1295, in render\n    self.environment.handle_exception()\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\environment.py", line 942, in handle_exception\n    raise rewrite_traceback_stack(source=source)\n  File "E:\\device repair manaement system - Copy\\app\\templates\\activate.html", line 1, in top-level template code\n    {% extends "base.html" %}\n  File "E:\\device repair manaement system - Copy\\app\\templates\\base.html", line 2, in top-level template code\n    {% set current_language = get_locale() %}\n    ^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\utils.py", line 92, in from_obj\n    if hasattr(obj, "jinja_pass_arg"):\n       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\njinja2.exceptions.UndefinedError: \'get_locale\' is undefined\n'}
2025-05-31 11:49:01,639 - device_repair_system - ERROR - Server Error: {'timestamp': '2025-05-31T11:49:01.636691', 'status_code': 500, 'error_type': 'UndefinedError', 'error_message': "'get_locale' is undefined", 'url': 'http://127.0.0.1:5000/activate', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous', 'traceback': 'Traceback (most recent call last):\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1484, in full_dispatch_request\n    rv = self.dispatch_request()\n         ^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1469, in dispatch_request\n    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "E:\\device repair manaement system - Copy\\app\\routes\\main.py", line 29, in activate_form\n    return render_template(\'activate.html\')\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\templating.py", line 151, in render_template\n    return _render(app, template, context)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\templating.py", line 132, in _render\n    rv = template.render(context)\n         ^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\environment.py", line 1295, in render\n    self.environment.handle_exception()\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\environment.py", line 942, in handle_exception\n    raise rewrite_traceback_stack(source=source)\n  File "E:\\device repair manaement system - Copy\\app\\templates\\activate.html", line 1, in top-level template code\n    {% extends "base.html" %}\n  File "E:\\device repair manaement system - Copy\\app\\templates\\base.html", line 2, in top-level template code\n    {% set current_language = get_locale() %}\n    ^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\utils.py", line 92, in from_obj\n    if hasattr(obj, "jinja_pass_arg"):\n       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\njinja2.exceptions.UndefinedError: \'get_locale\' is undefined\n'}
2025-05-31 11:49:45,414 - device_repair_system - INFO - Error handling system initialized
2025-05-31 11:50:05,014 - device_repair_system - INFO - Error handling system initialized
2025-05-31 11:50:08,286 - device_repair_system - INFO - Error handling system initialized
2025-05-31 11:54:40,084 - device_repair_system - INFO - Error handling system initialized
2025-05-31 11:54:42,781 - device_repair_system - INFO - Error handling system initialized
2025-05-31 11:59:28,152 - device_repair_system - INFO - Error handling system initialized
2025-05-31 11:59:31,600 - device_repair_system - INFO - Error handling system initialized
2025-05-31 12:00:49,777 - device_repair_system - INFO - Error handling system initialized
2025-05-31 12:04:43,885 - device_repair_system - INFO - Error handling system initialized
2025-05-31 12:04:46,725 - device_repair_system - INFO - Error handling system initialized
2025-05-31 12:07:13,560 - device_repair_system - INFO - Error handling system initialized
2025-05-31 12:07:32,050 - device_repair_system - INFO - Error handling system initialized
2025-05-31 12:07:55,716 - device_repair_system - INFO - Error handling system initialized
2025-05-31 12:07:58,014 - device_repair_system - INFO - Error handling system initialized
2025-05-31 12:16:22,157 - device_repair_system - INFO - Error handling system initialized
2025-05-31 12:16:22,175 - device_repair_system - INFO - Error handling system initialized
2025-05-31 12:17:24,131 - device_repair_system - INFO - Error handling system initialized
2025-05-31 12:17:26,546 - device_repair_system - INFO - Error handling system initialized
2025-05-31 12:18:05,720 - device_repair_system - INFO - Error handling system initialized
2025-05-31 12:18:43,333 - device_repair_system - INFO - Error handling system initialized
2025-05-31 12:21:04,315 - device_repair_system - INFO - Error handling system initialized
2025-05-31 12:21:06,954 - device_repair_system - INFO - Error handling system initialized
2025-05-31 12:39:52,955 - device_repair_system - INFO - Error handling system initialized
2025-05-31 12:39:55,236 - device_repair_system - INFO - Error handling system initialized
2025-05-31 12:43:43,809 - device_repair_system - INFO - Error handling system initialized
2025-05-31 12:43:46,284 - device_repair_system - INFO - Error handling system initialized
2025-05-31 12:44:05,561 - device_repair_system - INFO - Error handling system initialized
2025-05-31 12:44:07,977 - device_repair_system - INFO - Error handling system initialized
2025-05-31 12:45:13,040 - device_repair_system - INFO - Error handling system initialized
2025-05-31 12:49:28,210 - device_repair_system - INFO - Error handling system initialized
2025-05-31 12:49:30,920 - device_repair_system - INFO - Error handling system initialized
2025-05-31 12:50:47,031 - device_repair_system - WARNING - Client Error: {'timestamp': '2025-05-31T12:50:47.030641', 'status_code': 400, 'error_type': 'CSRFError', 'error_message': '400 Bad Request: The CSRF tokens do not match.', 'url': 'http://127.0.0.1:5000/activate', 'method': 'POST', 'user_agent': 'python-requests/2.31.0', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous'}
2025-05-31 12:51:11,219 - device_repair_system - INFO - Error handling system initialized
2025-05-31 12:51:23,214 - device_repair_system - WARNING - Client Error: {'timestamp': '2025-05-31T12:51:23.214699', 'status_code': 400, 'error_type': 'CSRFError', 'error_message': '400 Bad Request: The CSRF tokens do not match.', 'url': 'http://127.0.0.1:5000/activate', 'method': 'POST', 'user_agent': 'python-requests/2.31.0', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous'}
2025-05-31 12:51:41,515 - device_repair_system - INFO - Error handling system initialized
2025-05-31 12:51:43,909 - device_repair_system - INFO - Error handling system initialized
2025-05-31 12:53:26,810 - device_repair_system - INFO - Error handling system initialized
2025-05-31 12:53:58,410 - device_repair_system - WARNING - Client Error: {'timestamp': '2025-05-31T12:53:58.410609', 'status_code': 400, 'error_type': 'CSRFError', 'error_message': '400 Bad Request: The CSRF token is missing.', 'url': 'http://127.0.0.1:5000/api/activation-sync/notify-activation', 'method': 'POST', 'user_agent': 'python-requests/2.31.0', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous'}
2025-05-31 12:55:10,687 - device_repair_system - INFO - Error handling system initialized
2025-05-31 12:58:14,540 - device_repair_system - INFO - Error handling system initialized
2025-05-31 12:59:08,202 - device_repair_system - INFO - Error handling system initialized
2025-05-31 13:01:28,674 - device_repair_system - INFO - Error handling system initialized
2025-05-31 13:07:25,393 - device_repair_system - WARNING - Client Error: {'timestamp': '2025-05-31T13:07:25.392997', 'status_code': 400, 'error_type': 'CSRFError', 'error_message': '400 Bad Request: The CSRF token is missing.', 'url': 'http://127.0.0.1:5000/api/activation-sync/notify-activation', 'method': 'POST', 'user_agent': 'python-requests/2.31.0', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous'}
2025-05-31 13:09:58,217 - device_repair_system - INFO - Error handling system initialized
2025-05-31 13:10:07,423 - device_repair_system - WARNING - Client Error: {'timestamp': '2025-05-31T13:10:07.423034', 'status_code': 404, 'error_type': 'NotFound', 'error_message': '404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.', 'url': 'http://127.0.0.1:5000/favicon.ico', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous'}
2025-05-31 13:13:20,075 - device_repair_system - INFO - Error handling system initialized
2025-05-31 13:13:49,751 - device_repair_system - INFO - Error handling system initialized
2025-05-31 13:17:27,856 - device_repair_system - INFO - Error handling system initialized
2025-05-31 13:18:00,097 - device_repair_system - INFO - Error handling system initialized
2025-05-31 13:22:05,880 - device_repair_system - INFO - Error handling system initialized
2025-05-31 13:32:40,695 - device_repair_system - INFO - Error handling system initialized
2025-05-31 13:34:14,479 - device_repair_system - INFO - Error handling system initialized
2025-05-31 13:34:27,363 - device_repair_system - WARNING - Client Error: {'timestamp': '2025-05-31T13:34:27.363971', 'status_code': 404, 'error_type': 'NotFound', 'error_message': '404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.', 'url': 'http://127.0.0.1:5000/favicon.ico', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous'}
2025-05-31 13:38:43,434 - device_repair_system - INFO - Error handling system initialized
2025-05-31 13:44:35,252 - device_repair_system - INFO - Error handling system initialized
2025-05-31 13:47:02,961 - device_repair_system - INFO - Error handling system initialized
2025-05-31 13:49:49,209 - device_repair_system - WARNING - Client Error: {'timestamp': '2025-05-31T13:49:49.208336', 'status_code': 404, 'error_type': 'NotFound', 'error_message': '404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.', 'url': 'http://127.0.0.1:5000/favicon.ico', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous'}
2025-05-31 13:54:58,264 - device_repair_system - INFO - Error handling system initialized
2025-05-31 13:57:41,595 - device_repair_system - INFO - Error handling system initialized
2025-05-31 14:29:18,354 - device_repair_system - INFO - Error handling system initialized
2025-05-31 14:29:32,333 - device_repair_system - ERROR - Server Error: {'timestamp': '2025-05-31T14:29:32.244208', 'status_code': 500, 'error_type': 'TemplateSyntaxError', 'error_message': "Encountered unknown tag 'endblock'. You probably made a nesting mistake. Jinja is expecting this tag, but currently looking for 'endif'. The innermost block that needs to be closed is 'if'.", 'url': 'http://127.0.0.1:5000/activate', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous', 'traceback': 'Traceback (most recent call last):\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1484, in full_dispatch_request\n    rv = self.dispatch_request()\n         ^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1469, in dispatch_request\n    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "E:\\device repair manaement system - Copy\\app\\routes\\main.py", line 25, in activate_form\n    return render_template(\'activate.html\')\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\templating.py", line 150, in render_template\n    template = app.jinja_env.get_or_select_template(template_name_or_list)\n               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\environment.py", line 1087, in get_or_select_template\n    return self.get_template(template_name_or_list, parent, globals)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\environment.py", line 1016, in get_template\n    return self._load_template(name, globals)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\environment.py", line 975, in _load_template\n    template = self.loader.load(self, name, self.make_globals(globals))\n               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\loaders.py", line 138, in load\n    code = environment.compile(source, name, filename)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\environment.py", line 771, in compile\n    self.handle_exception(source=source_hint)\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\environment.py", line 942, in handle_exception\n    raise rewrite_traceback_stack(source=source)\n  File "E:\\device repair manaement system - Copy\\app\\templates\\activate.html", line 456, in template\n    {% endblock %}\njinja2.exceptions.TemplateSyntaxError: Encountered unknown tag \'endblock\'. You probably made a nesting mistake. Jinja is expecting this tag, but currently looking for \'endif\'. The innermost block that needs to be closed is \'if\'.\n'}
2025-05-31 14:29:32,972 - device_repair_system - WARNING - Client Error: {'timestamp': '2025-05-31T14:29:32.972900', 'status_code': 404, 'error_type': 'NotFound', 'error_message': '404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.', 'url': 'http://127.0.0.1:5000/favicon.ico', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous'}
2025-05-31 14:30:51,369 - device_repair_system - WARNING - Client Error: {'timestamp': '2025-05-31T14:30:51.369362', 'status_code': 400, 'error_type': 'CSRFError', 'error_message': '400 Bad Request: The CSRF token is missing.', 'url': 'http://127.0.0.1:5000/api/activation-sync/sync-new-code', 'method': 'POST', 'user_agent': 'python-requests/2.31.0', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous'}
2025-05-31 14:31:05,357 - device_repair_system - ERROR - Server Error: {'timestamp': '2025-05-31T14:31:05.356505', 'status_code': 500, 'error_type': 'TemplateSyntaxError', 'error_message': "Encountered unknown tag 'endblock'. You probably made a nesting mistake. Jinja is expecting this tag, but currently looking for 'endif'. The innermost block that needs to be closed is 'if'.", 'url': 'http://127.0.0.1:5000/activate', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous', 'traceback': 'Traceback (most recent call last):\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1484, in full_dispatch_request\n    rv = self.dispatch_request()\n         ^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1469, in dispatch_request\n    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "E:\\device repair manaement system - Copy\\app\\routes\\main.py", line 25, in activate_form\n    return render_template(\'activate.html\')\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\templating.py", line 150, in render_template\n    template = app.jinja_env.get_or_select_template(template_name_or_list)\n               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\environment.py", line 1087, in get_or_select_template\n    return self.get_template(template_name_or_list, parent, globals)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\environment.py", line 1016, in get_template\n    return self._load_template(name, globals)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\environment.py", line 975, in _load_template\n    template = self.loader.load(self, name, self.make_globals(globals))\n               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\loaders.py", line 138, in load\n    code = environment.compile(source, name, filename)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\environment.py", line 771, in compile\n    self.handle_exception(source=source_hint)\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\environment.py", line 942, in handle_exception\n    raise rewrite_traceback_stack(source=source)\n  File "E:\\device repair manaement system - Copy\\app\\templates\\activate.html", line 456, in template\n    {% endblock %}\njinja2.exceptions.TemplateSyntaxError: Encountered unknown tag \'endblock\'. You probably made a nesting mistake. Jinja is expecting this tag, but currently looking for \'endif\'. The innermost block that needs to be closed is \'if\'.\n'}
2025-05-31 15:10:53,663 - device_repair_system - INFO - Error handling system initialized
2025-05-31 15:11:01,021 - device_repair_system - ERROR - Server Error: {'timestamp': '2025-05-31T15:11:00.862536', 'status_code': 500, 'error_type': 'TemplateSyntaxError', 'error_message': "Encountered unknown tag 'endblock'. You probably made a nesting mistake. Jinja is expecting this tag, but currently looking for 'endif'. The innermost block that needs to be closed is 'if'.", 'url': 'http://127.0.0.1:5000/activate', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous', 'traceback': 'Traceback (most recent call last):\n  File "E:\\device repair manaement system - Copy\\.venv\\Lib\\site-packages\\flask\\app.py", line 1484, in full_dispatch_request\n    rv = self.dispatch_request()\n         ^^^^^^^^^^^^^^^^^^^^^^^\n  File "E:\\device repair manaement system - Copy\\.venv\\Lib\\site-packages\\flask\\app.py", line 1469, in dispatch_request\n    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "E:\\device repair manaement system - Copy\\app\\routes\\main.py", line 25, in activate_form\n    return render_template(\'activate.html\')\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "E:\\device repair manaement system - Copy\\.venv\\Lib\\site-packages\\flask\\templating.py", line 150, in render_template\n    template = app.jinja_env.get_or_select_template(template_name_or_list)\n               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "E:\\device repair manaement system - Copy\\.venv\\Lib\\site-packages\\jinja2\\environment.py", line 1087, in get_or_select_template\n    return self.get_template(template_name_or_list, parent, globals)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "E:\\device repair manaement system - Copy\\.venv\\Lib\\site-packages\\jinja2\\environment.py", line 1016, in get_template\n    return self._load_template(name, globals)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "E:\\device repair manaement system - Copy\\.venv\\Lib\\site-packages\\jinja2\\environment.py", line 975, in _load_template\n    template = self.loader.load(self, name, self.make_globals(globals))\n               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "E:\\device repair manaement system - Copy\\.venv\\Lib\\site-packages\\jinja2\\loaders.py", line 138, in load\n    code = environment.compile(source, name, filename)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "E:\\device repair manaement system - Copy\\.venv\\Lib\\site-packages\\jinja2\\environment.py", line 771, in compile\n    self.handle_exception(source=source_hint)\n  File "E:\\device repair manaement system - Copy\\.venv\\Lib\\site-packages\\jinja2\\environment.py", line 942, in handle_exception\n    raise rewrite_traceback_stack(source=source)\n  File "E:\\device repair manaement system - Copy\\app\\templates\\activate.html", line 456, in template\n    {% endblock %}\njinja2.exceptions.TemplateSyntaxError: Encountered unknown tag \'endblock\'. You probably made a nesting mistake. Jinja is expecting this tag, but currently looking for \'endif\'. The innermost block that needs to be closed is \'if\'.\n'}
2025-05-31 15:13:52,224 - device_repair_system - INFO - Error handling system initialized
2025-05-31 15:20:33,797 - device_repair_system - INFO - Error handling system initialized
2025-05-31 15:21:48,476 - device_repair_system - INFO - Error handling system initialized
2025-05-31 15:26:18,407 - device_repair_system - ERROR - Server Error: {'timestamp': '2025-05-31T15:26:18.400128', 'status_code': 500, 'error_type': 'TemplateNotFound', 'error_message': 'activate.html', 'url': 'http://127.0.0.1:5000/activate', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous', 'traceback': 'Traceback (most recent call last):\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1484, in full_dispatch_request\n    rv = self.dispatch_request()\n         ^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1469, in dispatch_request\n    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "E:\\device repair manaement system - Copy\\app\\routes\\main.py", line 25, in activate_form\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\templating.py", line 150, in render_template\n    template = app.jinja_env.get_or_select_template(template_name_or_list)\n               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\environment.py", line 1087, in get_or_select_template\n    return self.get_template(template_name_or_list, parent, globals)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\environment.py", line 1016, in get_template\n    return self._load_template(name, globals)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\environment.py", line 975, in _load_template\n    template = self.loader.load(self, name, self.make_globals(globals))\n               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\loaders.py", line 126, in load\n    source, filename, uptodate = self.get_source(environment, name)\n                                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\templating.py", line 64, in get_source\n    return self._get_source_fast(environment, template)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\templating.py", line 98, in _get_source_fast\n    raise TemplateNotFound(template)\njinja2.exceptions.TemplateNotFound: activate.html\n'}
2025-05-31 15:26:41,728 - device_repair_system - INFO - Error handling system initialized
2025-05-31 15:27:08,257 - device_repair_system - ERROR - Server Error: {'timestamp': '2025-05-31T15:27:08.255769', 'status_code': 500, 'error_type': 'TemplateNotFound', 'error_message': 'activate.html', 'url': 'http://127.0.0.1:5000/activate', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous', 'traceback': 'Traceback (most recent call last):\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1484, in full_dispatch_request\n    rv = self.dispatch_request()\n         ^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1469, in dispatch_request\n    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "E:\\device repair manaement system - Copy\\app\\routes\\main.py", line 25, in activate_form\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\templating.py", line 150, in render_template\n    template = app.jinja_env.get_or_select_template(template_name_or_list)\n               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\environment.py", line 1087, in get_or_select_template\n    return self.get_template(template_name_or_list, parent, globals)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\environment.py", line 1016, in get_template\n    return self._load_template(name, globals)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\environment.py", line 975, in _load_template\n    template = self.loader.load(self, name, self.make_globals(globals))\n               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\loaders.py", line 126, in load\n    source, filename, uptodate = self.get_source(environment, name)\n                                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\templating.py", line 64, in get_source\n    return self._get_source_fast(environment, template)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\templating.py", line 98, in _get_source_fast\n    raise TemplateNotFound(template)\njinja2.exceptions.TemplateNotFound: activate.html\n'}
2025-05-31 15:27:45,551 - device_repair_system - ERROR - Server Error: {'timestamp': '2025-05-31T15:27:45.550983', 'status_code': 500, 'error_type': 'TemplateNotFound', 'error_message': 'activate.html', 'url': 'http://127.0.0.1:5000/activate', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous', 'traceback': 'Traceback (most recent call last):\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1484, in full_dispatch_request\n    rv = self.dispatch_request()\n         ^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1469, in dispatch_request\n    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "E:\\device repair manaement system - Copy\\app\\routes\\main.py", line 25, in activate_form\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\templating.py", line 150, in render_template\n    template = app.jinja_env.get_or_select_template(template_name_or_list)\n               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\environment.py", line 1087, in get_or_select_template\n    return self.get_template(template_name_or_list, parent, globals)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\environment.py", line 1016, in get_template\n    return self._load_template(name, globals)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\environment.py", line 975, in _load_template\n    template = self.loader.load(self, name, self.make_globals(globals))\n               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\loaders.py", line 126, in load\n    source, filename, uptodate = self.get_source(environment, name)\n                                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\templating.py", line 64, in get_source\n    return self._get_source_fast(environment, template)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\templating.py", line 98, in _get_source_fast\n    raise TemplateNotFound(template)\njinja2.exceptions.TemplateNotFound: activate.html\n'}
2025-05-31 15:27:46,383 - device_repair_system - ERROR - Server Error: {'timestamp': '2025-05-31T15:27:46.382757', 'status_code': 500, 'error_type': 'TemplateNotFound', 'error_message': 'activate.html', 'url': 'http://127.0.0.1:5000/activate', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous', 'traceback': 'Traceback (most recent call last):\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1484, in full_dispatch_request\n    rv = self.dispatch_request()\n         ^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1469, in dispatch_request\n    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "E:\\device repair manaement system - Copy\\app\\routes\\main.py", line 25, in activate_form\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\templating.py", line 150, in render_template\n    template = app.jinja_env.get_or_select_template(template_name_or_list)\n               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\environment.py", line 1087, in get_or_select_template\n    return self.get_template(template_name_or_list, parent, globals)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\environment.py", line 1016, in get_template\n    return self._load_template(name, globals)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\environment.py", line 975, in _load_template\n    template = self.loader.load(self, name, self.make_globals(globals))\n               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\loaders.py", line 126, in load\n    source, filename, uptodate = self.get_source(environment, name)\n                                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\templating.py", line 64, in get_source\n    return self._get_source_fast(environment, template)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\templating.py", line 98, in _get_source_fast\n    raise TemplateNotFound(template)\njinja2.exceptions.TemplateNotFound: activate.html\n'}
2025-05-31 15:30:18,227 - device_repair_system - INFO - Error handling system initialized
2025-05-31 15:30:44,152 - device_repair_system - ERROR - Server Error: {'timestamp': '2025-05-31T15:30:44.151484', 'status_code': 500, 'error_type': 'TemplateNotFound', 'error_message': 'activate.html', 'url': 'http://127.0.0.1:5000/activate', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous', 'traceback': 'Traceback (most recent call last):\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1484, in full_dispatch_request\n    rv = self.dispatch_request()\n         ^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1469, in dispatch_request\n    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "E:\\device repair manaement system - Copy\\app\\routes\\main.py", line 25, in activate_form\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\templating.py", line 150, in render_template\n    template = app.jinja_env.get_or_select_template(template_name_or_list)\n               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\environment.py", line 1087, in get_or_select_template\n    return self.get_template(template_name_or_list, parent, globals)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\environment.py", line 1016, in get_template\n    return self._load_template(name, globals)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\environment.py", line 975, in _load_template\n    template = self.loader.load(self, name, self.make_globals(globals))\n               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\loaders.py", line 126, in load\n    source, filename, uptodate = self.get_source(environment, name)\n                                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\templating.py", line 64, in get_source\n    return self._get_source_fast(environment, template)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\templating.py", line 98, in _get_source_fast\n    raise TemplateNotFound(template)\njinja2.exceptions.TemplateNotFound: activate.html\n'}
2025-05-31 15:32:33,213 - device_repair_system - ERROR - Server Error: {'timestamp': '2025-05-31T15:32:33.211053', 'status_code': 500, 'error_type': 'TemplateNotFound', 'error_message': 'activate.html', 'url': 'http://127.0.0.1:5000/activate', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous', 'traceback': 'Traceback (most recent call last):\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1484, in full_dispatch_request\n    rv = self.dispatch_request()\n         ^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1469, in dispatch_request\n    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "E:\\device repair manaement system - Copy\\app\\routes\\main.py", line 25, in activate_form\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\templating.py", line 150, in render_template\n    template = app.jinja_env.get_or_select_template(template_name_or_list)\n               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\environment.py", line 1087, in get_or_select_template\n    return self.get_template(template_name_or_list, parent, globals)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\environment.py", line 1016, in get_template\n    return self._load_template(name, globals)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\environment.py", line 975, in _load_template\n    template = self.loader.load(self, name, self.make_globals(globals))\n               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\loaders.py", line 126, in load\n    source, filename, uptodate = self.get_source(environment, name)\n                                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\templating.py", line 64, in get_source\n    return self._get_source_fast(environment, template)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\templating.py", line 98, in _get_source_fast\n    raise TemplateNotFound(template)\njinja2.exceptions.TemplateNotFound: activate.html\n'}
2025-05-31 15:34:52,704 - device_repair_system - INFO - Error handling system initialized
2025-05-31 15:35:20,761 - device_repair_system - ERROR - Server Error: {'timestamp': '2025-05-31T15:35:20.674324', 'status_code': 500, 'error_type': 'BuildError', 'error_message': "Could not build url for endpoint 'dashboard.dashboard_home'. Did you mean 'dashboard.api_dashboard_data' instead?", 'url': 'http://127.0.0.1:5000/', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous', 'traceback': 'Traceback (most recent call last):\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1484, in full_dispatch_request\n    rv = self.dispatch_request()\n         ^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1469, in dispatch_request\n    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "E:\\device repair manaement system - Copy\\app\\routes\\main.py", line 9, in index\n    return redirect(url_for(\'dashboard.dashboard_home\'))\n                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\helpers.py", line 225, in url_for\n    return current_app.url_for(\n           ^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1697, in url_for\n    return self.handle_url_build_error(error, endpoint, values)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1686, in url_for\n    rv = url_adapter.build(  # type: ignore[union-attr]\n         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\werkzeug\\routing\\map.py", line 950, in build\n    raise BuildError(endpoint, values, method, self)\nwerkzeug.routing.exceptions.BuildError: Could not build url for endpoint \'dashboard.dashboard_home\'. Did you mean \'dashboard.api_dashboard_data\' instead?\n'}
2025-05-31 15:36:11,599 - device_repair_system - ERROR - Server Error: {'timestamp': '2025-05-31T15:36:11.587314', 'status_code': 500, 'error_type': 'BuildError', 'error_message': "Could not build url for endpoint 'dashboard.dashboard_home'. Did you mean 'dashboard.api_dashboard_data' instead?", 'url': 'http://127.0.0.1:5000/', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous', 'traceback': 'Traceback (most recent call last):\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1484, in full_dispatch_request\n    rv = self.dispatch_request()\n         ^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1469, in dispatch_request\n    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "E:\\device repair manaement system - Copy\\app\\routes\\main.py", line 9, in index\n    return redirect(url_for(\'dashboard.index\'))\n                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\helpers.py", line 225, in url_for\n    return current_app.url_for(\n           ^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1697, in url_for\n    return self.handle_url_build_error(error, endpoint, values)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1686, in url_for\n    rv = url_adapter.build(  # type: ignore[union-attr]\n         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\werkzeug\\routing\\map.py", line 950, in build\n    raise BuildError(endpoint, values, method, self)\nwerkzeug.routing.exceptions.BuildError: Could not build url for endpoint \'dashboard.dashboard_home\'. Did you mean \'dashboard.api_dashboard_data\' instead?\n'}
2025-05-31 15:36:44,327 - device_repair_system - INFO - Error handling system initialized
2025-05-31 15:37:19,080 - device_repair_system - ERROR - Server Error: {'timestamp': '2025-05-31T15:37:19.072319', 'status_code': 500, 'error_type': 'BuildError', 'error_message': "Could not build url for endpoint 'index'. Did you mean 'main.index' instead?", 'url': 'http://127.0.0.1:5000/auth/login?next=/dashboard/', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous', 'traceback': 'Traceback (most recent call last):\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1484, in full_dispatch_request\n    rv = self.dispatch_request()\n         ^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1469, in dispatch_request\n    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "E:\\device repair manaement system - Copy\\app\\routes\\auth.py", line 63, in login\n    return render_template(\'auth/login.html\')\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\templating.py", line 151, in render_template\n    return _render(app, template, context)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\templating.py", line 132, in _render\n    rv = template.render(context)\n         ^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\environment.py", line 1295, in render\n    self.environment.handle_exception()\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\environment.py", line 942, in handle_exception\n    raise rewrite_traceback_stack(source=source)\n  File "E:\\device repair manaement system - Copy\\app\\templates\\auth\\login.html", line 1, in top-level template code\n    {% extends \'base.html\' %}\n  File "E:\\device repair manaement system - Copy\\app\\templates\\base.html", line 279, in top-level template code\n    <a class="navbar-brand animate__animated animate__fadeIn" href="{{ url_for(\'index\') }}">\n    ^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1697, in url_for\n    return self.handle_url_build_error(error, endpoint, values)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1686, in url_for\n    rv = url_adapter.build(  # type: ignore[union-attr]\n         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\werkzeug\\routing\\map.py", line 950, in build\n    raise BuildError(endpoint, values, method, self)\nwerkzeug.routing.exceptions.BuildError: Could not build url for endpoint \'index\'. Did you mean \'main.index\' instead?\n'}
2025-05-31 15:38:26,036 - device_repair_system - ERROR - Server Error: {'timestamp': '2025-05-31T15:38:26.023196', 'status_code': 500, 'error_type': 'BuildError', 'error_message': "Could not build url for endpoint 'index'. Did you mean 'main.index' instead?", 'url': 'http://127.0.0.1:5000/auth/login?next=/dashboard/', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous', 'traceback': 'Traceback (most recent call last):\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1484, in full_dispatch_request\n    rv = self.dispatch_request()\n         ^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1469, in dispatch_request\n    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "E:\\device repair manaement system - Copy\\app\\routes\\auth.py", line 63, in login\n    return render_template(\'auth/login.html\')\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\templating.py", line 151, in render_template\n    return _render(app, template, context)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\templating.py", line 132, in _render\n    rv = template.render(context)\n         ^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\environment.py", line 1295, in render\n    self.environment.handle_exception()\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\environment.py", line 942, in handle_exception\n    raise rewrite_traceback_stack(source=source)\n  File "E:\\device repair manaement system - Copy\\app\\templates\\auth\\login.html", line 1, in top-level template code\n    {% extends \'base.html\' %}\n  File "E:\\device repair manaement system - Copy\\app\\templates\\base.html", line 279, in top-level template code\n    <a class="navbar-brand animate__animated animate__fadeIn" href="{{ url_for(\'main.index\') }}">\n    ^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1697, in url_for\n    return self.handle_url_build_error(error, endpoint, values)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1686, in url_for\n    rv = url_adapter.build(  # type: ignore[union-attr]\n         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\werkzeug\\routing\\map.py", line 950, in build\n    raise BuildError(endpoint, values, method, self)\nwerkzeug.routing.exceptions.BuildError: Could not build url for endpoint \'index\'. Did you mean \'main.index\' instead?\n'}
2025-05-31 15:39:14,555 - device_repair_system - INFO - Error handling system initialized
2025-05-31 15:55:02,095 - device_repair_system - WARNING - Client Error: {'timestamp': '2025-05-31T15:55:02.095439', 'status_code': 404, 'error_type': 'NotFound', 'error_message': '404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.', 'url': 'http://127.0.0.1:5000/subscription-dashboard', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous'}
2025-05-31 15:55:03,616 - device_repair_system - WARNING - Client Error: {'timestamp': '2025-05-31T15:55:03.616901', 'status_code': 404, 'error_type': 'NotFound', 'error_message': '404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.', 'url': 'http://127.0.0.1:5000/subscription-dashboard', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous'}
2025-05-31 16:10:45,495 - device_repair_system - INFO - Error handling system initialized
2025-05-31 16:11:01,377 - device_repair_system - INFO - Error handling system initialized
2025-05-31 16:18:18,347 - device_repair_system - INFO - Error handling system initialized
2025-05-31 16:29:07,409 - device_repair_system - INFO - Error handling system initialized
2025-05-31 16:36:12,046 - device_repair_system - INFO - Error handling system initialized
2025-05-31 16:41:12,199 - device_repair_system - INFO - Error handling system initialized
2025-05-31 16:41:13,579 - device_repair_system - INFO - Error handling system initialized
2025-05-31 16:43:58,979 - device_repair_system - INFO - Error handling system initialized
2025-05-31 16:49:13,718 - device_repair_system - INFO - Error handling system initialized
2025-05-31 16:52:24,089 - device_repair_system - INFO - Error handling system initialized
2025-05-31 17:17:04,077 - device_repair_system - INFO - Error handling system initialized
2025-05-31 17:20:50,631 - device_repair_system - INFO - Error handling system initialized
2025-05-31 17:21:13,206 - device_repair_system - INFO - Error handling system initialized
2025-05-31 17:23:25,214 - device_repair_system - INFO - Error handling system initialized
2025-05-31 17:24:00,952 - device_repair_system - INFO - Error handling system initialized
2025-05-31 17:26:51,215 - device_repair_system - INFO - Error handling system initialized
2025-05-31 17:36:14,151 - device_repair_system - INFO - Error handling system initialized
2025-05-31 17:43:50,166 - device_repair_system - INFO - Error handling system initialized
2025-05-31 17:44:18,029 - device_repair_system - INFO - Error handling system initialized
2025-05-31 17:48:40,226 - device_repair_system - ERROR - Server Error: {'timestamp': '2025-05-31T17:48:40.071192', 'status_code': 500, 'error_type': 'TemplateSyntaxError', 'error_message': "Unexpected end of template. Jinja was looking for the following tags: 'endblock'. The innermost block that needs to be closed is 'block'.", 'url': 'http://127.0.0.1:5000/devices/1/tickets/21', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous', 'traceback': 'Traceback (most recent call last):\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1484, in full_dispatch_request\n    rv = self.dispatch_request()\n         ^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1469, in dispatch_request\n    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask_login\\utils.py", line 290, in decorated_view\n    return current_app.ensure_sync(func)(*args, **kwargs)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "E:\\device repair manaement system - Copy\\app\\routes\\device.py", line 688, in show_ticket\n    return render_template(\'device/show_ticket.html\', device=device, ticket=ticket)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\templating.py", line 150, in render_template\n    template = app.jinja_env.get_or_select_template(template_name_or_list)\n               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\environment.py", line 1087, in get_or_select_template\n    return self.get_template(template_name_or_list, parent, globals)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\environment.py", line 1016, in get_template\n    return self._load_template(name, globals)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\environment.py", line 975, in _load_template\n    template = self.loader.load(self, name, self.make_globals(globals))\n               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\loaders.py", line 138, in load\n    code = environment.compile(source, name, filename)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\environment.py", line 771, in compile\n    self.handle_exception(source=source_hint)\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\environment.py", line 942, in handle_exception\n    raise rewrite_traceback_stack(source=source)\n  File "E:\\device repair manaement system - Copy\\app\\templates\\device\\show_ticket.html", line 285, in template\n    {% endblock %}\njinja2.exceptions.TemplateSyntaxError: Unexpected end of template. Jinja was looking for the following tags: \'endblock\'. The innermost block that needs to be closed is \'block\'.\n'}
2025-05-31 17:48:40,849 - device_repair_system - WARNING - Client Error: {'timestamp': '2025-05-31T17:48:40.848115', 'status_code': 404, 'error_type': 'NotFound', 'error_message': '404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.', 'url': 'http://127.0.0.1:5000/favicon.ico', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous'}
2025-05-31 17:50:20,881 - device_repair_system - INFO - Error handling system initialized
2025-05-31 17:51:11,190 - device_repair_system - INFO - Error handling system initialized
2025-05-31 17:51:32,932 - device_repair_system - INFO - Error handling system initialized
2025-05-31 17:52:53,019 - device_repair_system - ERROR - Server Error: {'timestamp': '2025-05-31T17:52:53.018841', 'status_code': 500, 'error_type': 'TemplateSyntaxError', 'error_message': "Unexpected end of template. Jinja was looking for the following tags: 'endblock'. The innermost block that needs to be closed is 'block'.", 'url': 'http://127.0.0.1:5000/devices/1/tickets/21', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous', 'traceback': 'Traceback (most recent call last):\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1484, in full_dispatch_request\n    rv = self.dispatch_request()\n         ^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1469, in dispatch_request\n    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask_login\\utils.py", line 290, in decorated_view\n    return current_app.ensure_sync(func)(*args, **kwargs)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "E:\\device repair manaement system - Copy\\app\\routes\\device.py", line 688, in show_ticket\n    return render_template(\'device/show_ticket.html\', device=device, ticket=ticket)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\templating.py", line 150, in render_template\n    template = app.jinja_env.get_or_select_template(template_name_or_list)\n               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\environment.py", line 1087, in get_or_select_template\n    return self.get_template(template_name_or_list, parent, globals)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\environment.py", line 1016, in get_template\n    return self._load_template(name, globals)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\environment.py", line 975, in _load_template\n    template = self.loader.load(self, name, self.make_globals(globals))\n               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\loaders.py", line 138, in load\n    code = environment.compile(source, name, filename)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\environment.py", line 771, in compile\n    self.handle_exception(source=source_hint)\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\environment.py", line 942, in handle_exception\n    raise rewrite_traceback_stack(source=source)\n  File "E:\\device repair manaement system - Copy\\app\\templates\\device\\show_ticket.html", line 285, in template\n    {% endblock %}\njinja2.exceptions.TemplateSyntaxError: Unexpected end of template. Jinja was looking for the following tags: \'endblock\'. The innermost block that needs to be closed is \'block\'.\n'}
2025-05-31 17:53:06,358 - device_repair_system - ERROR - Server Error: {'timestamp': '2025-05-31T17:53:06.357624', 'status_code': 500, 'error_type': 'TemplateSyntaxError', 'error_message': "Unexpected end of template. Jinja was looking for the following tags: 'endblock'. The innermost block that needs to be closed is 'block'.", 'url': 'http://127.0.0.1:5000/devices/1/tickets/21', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous', 'traceback': 'Traceback (most recent call last):\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1484, in full_dispatch_request\n    rv = self.dispatch_request()\n         ^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1469, in dispatch_request\n    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask_login\\utils.py", line 290, in decorated_view\n    return current_app.ensure_sync(func)(*args, **kwargs)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "E:\\device repair manaement system - Copy\\app\\routes\\device.py", line 688, in show_ticket\n    return render_template(\'device/show_ticket.html\', device=device, ticket=ticket)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\templating.py", line 150, in render_template\n    template = app.jinja_env.get_or_select_template(template_name_or_list)\n               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\environment.py", line 1087, in get_or_select_template\n    return self.get_template(template_name_or_list, parent, globals)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\environment.py", line 1016, in get_template\n    return self._load_template(name, globals)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\environment.py", line 975, in _load_template\n    template = self.loader.load(self, name, self.make_globals(globals))\n               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\loaders.py", line 138, in load\n    code = environment.compile(source, name, filename)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\environment.py", line 771, in compile\n    self.handle_exception(source=source_hint)\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\environment.py", line 942, in handle_exception\n    raise rewrite_traceback_stack(source=source)\n  File "E:\\device repair manaement system - Copy\\app\\templates\\device\\show_ticket.html", line 285, in template\n    {% endblock %}\njinja2.exceptions.TemplateSyntaxError: Unexpected end of template. Jinja was looking for the following tags: \'endblock\'. The innermost block that needs to be closed is \'block\'.\n'}
2025-05-31 17:53:09,760 - device_repair_system - INFO - Error handling system initialized
2025-05-31 17:53:12,274 - device_repair_system - INFO - Error handling system initialized
2025-05-31 17:53:35,405 - device_repair_system - WARNING - Client Error: {'timestamp': '2025-05-31T17:53:35.405101', 'status_code': 404, 'error_type': 'NotFound', 'error_message': '404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.', 'url': 'http://127.0.0.1:5001/favicon.ico', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous'}
2025-05-31 17:53:45,172 - device_repair_system - WARNING - Client Error: {'timestamp': '2025-05-31T17:53:45.172814', 'status_code': 400, 'error_type': 'CSRFError', 'error_message': '400 Bad Request: The CSRF token is missing.', 'url': 'http://127.0.0.1:5001/test/update_cost/1/21', 'method': 'POST', 'user_agent': 'Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.19041.5915', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous'}
2025-05-31 17:54:38,463 - device_repair_system - INFO - Error handling system initialized
2025-05-31 17:55:31,243 - device_repair_system - ERROR - Server Error: {'timestamp': '2025-05-31T17:55:31.242885', 'status_code': 500, 'error_type': 'TemplateSyntaxError', 'error_message': "Unexpected end of template. Jinja was looking for the following tags: 'endblock'. The innermost block that needs to be closed is 'block'.", 'url': 'http://127.0.0.1:5000/devices/1/tickets/21', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous', 'traceback': 'Traceback (most recent call last):\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1484, in full_dispatch_request\n    rv = self.dispatch_request()\n         ^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1469, in dispatch_request\n    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask_login\\utils.py", line 290, in decorated_view\n    return current_app.ensure_sync(func)(*args, **kwargs)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "E:\\device repair manaement system - Copy\\app\\routes\\device.py", line 688, in show_ticket\n    return render_template(\'device/show_ticket.html\', device=device, ticket=ticket)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\templating.py", line 150, in render_template\n    template = app.jinja_env.get_or_select_template(template_name_or_list)\n               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\environment.py", line 1087, in get_or_select_template\n    return self.get_template(template_name_or_list, parent, globals)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\environment.py", line 1016, in get_template\n    return self._load_template(name, globals)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\environment.py", line 975, in _load_template\n    template = self.loader.load(self, name, self.make_globals(globals))\n               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\loaders.py", line 138, in load\n    code = environment.compile(source, name, filename)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\environment.py", line 771, in compile\n    self.handle_exception(source=source_hint)\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\environment.py", line 942, in handle_exception\n    raise rewrite_traceback_stack(source=source)\n  File "E:\\device repair manaement system - Copy\\app\\templates\\device\\show_ticket.html", line 285, in template\n    {% endblock %}\njinja2.exceptions.TemplateSyntaxError: Unexpected end of template. Jinja was looking for the following tags: \'endblock\'. The innermost block that needs to be closed is \'block\'.\n'}
2025-05-31 17:55:49,899 - device_repair_system - ERROR - Server Error: {'timestamp': '2025-05-31T17:55:49.895781', 'status_code': 500, 'error_type': 'TemplateSyntaxError', 'error_message': "Unexpected end of template. Jinja was looking for the following tags: 'endblock'. The innermost block that needs to be closed is 'block'.", 'url': 'http://127.0.0.1:5000/devices/1/tickets/21', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous', 'traceback': 'Traceback (most recent call last):\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1484, in full_dispatch_request\n    rv = self.dispatch_request()\n         ^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1469, in dispatch_request\n    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask_login\\utils.py", line 290, in decorated_view\n    return current_app.ensure_sync(func)(*args, **kwargs)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "E:\\device repair manaement system - Copy\\app\\routes\\device.py", line 688, in show_ticket\n    return render_template(\'device/show_ticket.html\', device=device, ticket=ticket)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\templating.py", line 150, in render_template\n    template = app.jinja_env.get_or_select_template(template_name_or_list)\n               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\environment.py", line 1087, in get_or_select_template\n    return self.get_template(template_name_or_list, parent, globals)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\environment.py", line 1016, in get_template\n    return self._load_template(name, globals)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\environment.py", line 975, in _load_template\n    template = self.loader.load(self, name, self.make_globals(globals))\n               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\loaders.py", line 138, in load\n    code = environment.compile(source, name, filename)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\environment.py", line 771, in compile\n    self.handle_exception(source=source_hint)\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\environment.py", line 942, in handle_exception\n    raise rewrite_traceback_stack(source=source)\n  File "E:\\device repair manaement system - Copy\\app\\templates\\device\\show_ticket.html", line 308, in template\n    {% endblock %}\njinja2.exceptions.TemplateSyntaxError: Unexpected end of template. Jinja was looking for the following tags: \'endblock\'. The innermost block that needs to be closed is \'block\'.\n'}
2025-05-31 17:58:49,889 - device_repair_system - INFO - Error handling system initialized
2025-05-31 17:59:15,624 - device_repair_system - ERROR - Server Error: {'timestamp': '2025-05-31T17:59:15.442887', 'status_code': 500, 'error_type': 'TemplateSyntaxError', 'error_message': "Unexpected end of template. Jinja was looking for the following tags: 'endblock'. The innermost block that needs to be closed is 'block'.", 'url': 'http://127.0.0.1:5000/devices/1/tickets/21', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous', 'traceback': 'Traceback (most recent call last):\n  File "E:\\device repair manaement system - Copy\\.venv\\Lib\\site-packages\\flask\\app.py", line 1484, in full_dispatch_request\n    rv = self.dispatch_request()\n         ^^^^^^^^^^^^^^^^^^^^^^^\n  File "E:\\device repair manaement system - Copy\\.venv\\Lib\\site-packages\\flask\\app.py", line 1469, in dispatch_request\n    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "E:\\device repair manaement system - Copy\\.venv\\Lib\\site-packages\\flask_login\\utils.py", line 290, in decorated_view\n    return current_app.ensure_sync(func)(*args, **kwargs)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "E:\\device repair manaement system - Copy\\app\\routes\\device.py", line 688, in show_ticket\n    return render_template(\'device/show_ticket.html\', device=device, ticket=ticket)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "E:\\device repair manaement system - Copy\\.venv\\Lib\\site-packages\\flask\\templating.py", line 150, in render_template\n    template = app.jinja_env.get_or_select_template(template_name_or_list)\n               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "E:\\device repair manaement system - Copy\\.venv\\Lib\\site-packages\\jinja2\\environment.py", line 1087, in get_or_select_template\n    return self.get_template(template_name_or_list, parent, globals)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "E:\\device repair manaement system - Copy\\.venv\\Lib\\site-packages\\jinja2\\environment.py", line 1016, in get_template\n    return self._load_template(name, globals)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "E:\\device repair manaement system - Copy\\.venv\\Lib\\site-packages\\jinja2\\environment.py", line 975, in _load_template\n    template = self.loader.load(self, name, self.make_globals(globals))\n               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "E:\\device repair manaement system - Copy\\.venv\\Lib\\site-packages\\jinja2\\loaders.py", line 138, in load\n    code = environment.compile(source, name, filename)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "E:\\device repair manaement system - Copy\\.venv\\Lib\\site-packages\\jinja2\\environment.py", line 771, in compile\n    self.handle_exception(source=source_hint)\n  File "E:\\device repair manaement system - Copy\\.venv\\Lib\\site-packages\\jinja2\\environment.py", line 942, in handle_exception\n    raise rewrite_traceback_stack(source=source)\n  File "E:\\device repair manaement system - Copy\\app\\templates\\device\\show_ticket.html", line 308, in template\n    {% endblock %}\njinja2.exceptions.TemplateSyntaxError: Unexpected end of template. Jinja was looking for the following tags: \'endblock\'. The innermost block that needs to be closed is \'block\'.\n'}
2025-05-31 17:59:16,132 - device_repair_system - WARNING - Client Error: {'timestamp': '2025-05-31T17:59:16.132553', 'status_code': 404, 'error_type': 'NotFound', 'error_message': '404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.', 'url': 'http://127.0.0.1:5000/favicon.ico', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous'}
2025-05-31 18:01:21,968 - device_repair_system - INFO - Error handling system initialized
2025-05-31 18:11:29,623 - device_repair_system - INFO - Error handling system initialized
2025-05-31 18:12:55,805 - device_repair_system - INFO - Error handling system initialized
2025-05-31 18:21:56,693 - device_repair_system - ERROR - Server Error: {'timestamp': '2025-05-31T18:21:56.600607', 'status_code': 500, 'error_type': 'BuildError', 'error_message': "Could not build url for endpoint 'device.get_cost_suggestions' with values ['device_id', 'ticket_id']. Did you mean 'device_api.get_device_statistics' instead?", 'url': 'http://127.0.0.1:5000/devices/1/tickets/21', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous', 'traceback': 'Traceback (most recent call last):\n  File "E:\\device repair manaement system - Copy\\.venv\\Lib\\site-packages\\flask\\app.py", line 1484, in full_dispatch_request\n    rv = self.dispatch_request()\n         ^^^^^^^^^^^^^^^^^^^^^^^\n  File "E:\\device repair manaement system - Copy\\.venv\\Lib\\site-packages\\flask\\app.py", line 1469, in dispatch_request\n    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "E:\\device repair manaement system - Copy\\.venv\\Lib\\site-packages\\flask_login\\utils.py", line 290, in decorated_view\n    return current_app.ensure_sync(func)(*args, **kwargs)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "E:\\device repair manaement system - Copy\\app\\routes\\device.py", line 688, in show_ticket\n    return render_template(\'device/show_ticket.html\', device=device, ticket=ticket)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "E:\\device repair manaement system - Copy\\.venv\\Lib\\site-packages\\flask\\templating.py", line 151, in render_template\n    return _render(app, template, context)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "E:\\device repair manaement system - Copy\\.venv\\Lib\\site-packages\\flask\\templating.py", line 132, in _render\n    rv = template.render(context)\n         ^^^^^^^^^^^^^^^^^^^^^^^^\n  File "E:\\device repair manaement system - Copy\\.venv\\Lib\\site-packages\\jinja2\\environment.py", line 1295, in render\n    self.environment.handle_exception()\n  File "E:\\device repair manaement system - Copy\\.venv\\Lib\\site-packages\\jinja2\\environment.py", line 942, in handle_exception\n    raise rewrite_traceback_stack(source=source)\n  File "E:\\device repair manaement system - Copy\\app\\templates\\device\\show_ticket.html", line 1, in top-level template code\n    {% extends \'base.html\' %}\n  File "E:\\device repair manaement system - Copy\\app\\templates\\base.html", line 605, in top-level template code\n    {% block scripts %}{% endblock %}\n    ^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "E:\\device repair manaement system - Copy\\app\\templates\\device\\show_ticket.html", line 587, in block \'scripts\'\n    fetch(\'{{ url_for("device.get_cost_suggestions", device_id=device.id, ticket_id=ticket.id) }}\')\n    ^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "E:\\device repair manaement system - Copy\\.venv\\Lib\\site-packages\\flask\\app.py", line 1697, in url_for\n    return self.handle_url_build_error(error, endpoint, values)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "E:\\device repair manaement system - Copy\\.venv\\Lib\\site-packages\\flask\\app.py", line 1686, in url_for\n    rv = url_adapter.build(  # type: ignore[union-attr]\n         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "E:\\device repair manaement system - Copy\\.venv\\Lib\\site-packages\\werkzeug\\routing\\map.py", line 950, in build\n    raise BuildError(endpoint, values, method, self)\nwerkzeug.routing.exceptions.BuildError: Could not build url for endpoint \'device.get_cost_suggestions\' with values [\'device_id\', \'ticket_id\']. Did you mean \'device_api.get_device_statistics\' instead?\n'}
2025-05-31 18:23:38,654 - device_repair_system - INFO - Error handling system initialized
2025-05-31 18:23:53,167 - device_repair_system - ERROR - Server Error: {'timestamp': '2025-05-31T18:23:53.160612', 'status_code': 500, 'error_type': 'BuildError', 'error_message': "Could not build url for endpoint 'device.get_cost_suggestions' with values ['device_id', 'ticket_id']. Did you mean 'device_api.get_device_statistics' instead?", 'url': 'http://127.0.0.1:5000/devices/1/tickets/21', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous', 'traceback': 'Traceback (most recent call last):\n  File "E:\\device repair manaement system - Copy\\.venv\\Lib\\site-packages\\flask\\app.py", line 1484, in full_dispatch_request\n    rv = self.dispatch_request()\n         ^^^^^^^^^^^^^^^^^^^^^^^\n  File "E:\\device repair manaement system - Copy\\.venv\\Lib\\site-packages\\flask\\app.py", line 1469, in dispatch_request\n    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "E:\\device repair manaement system - Copy\\.venv\\Lib\\site-packages\\flask_login\\utils.py", line 290, in decorated_view\n    return current_app.ensure_sync(func)(*args, **kwargs)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "E:\\device repair manaement system - Copy\\app\\routes\\device.py", line 688, in show_ticket\n    return render_template(\'device/show_ticket.html\', device=device, ticket=ticket)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "E:\\device repair manaement system - Copy\\.venv\\Lib\\site-packages\\flask\\templating.py", line 151, in render_template\n    return _render(app, template, context)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "E:\\device repair manaement system - Copy\\.venv\\Lib\\site-packages\\flask\\templating.py", line 132, in _render\n    rv = template.render(context)\n         ^^^^^^^^^^^^^^^^^^^^^^^^\n  File "E:\\device repair manaement system - Copy\\.venv\\Lib\\site-packages\\jinja2\\environment.py", line 1295, in render\n    self.environment.handle_exception()\n  File "E:\\device repair manaement system - Copy\\.venv\\Lib\\site-packages\\jinja2\\environment.py", line 942, in handle_exception\n    raise rewrite_traceback_stack(source=source)\n  File "E:\\device repair manaement system - Copy\\app\\templates\\device\\show_ticket.html", line 1, in top-level template code\n    {% extends \'base.html\' %}\n  File "E:\\device repair manaement system - Copy\\app\\templates\\base.html", line 605, in top-level template code\n    {% block scripts %}{% endblock %}\n    ^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "E:\\device repair manaement system - Copy\\app\\templates\\device\\show_ticket.html", line 587, in block \'scripts\'\n    fetch(\'{{ url_for("device.get_cost_suggestions", device_id=device.id, ticket_id=ticket.id) }}\')\n    ^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "E:\\device repair manaement system - Copy\\.venv\\Lib\\site-packages\\flask\\app.py", line 1697, in url_for\n    return self.handle_url_build_error(error, endpoint, values)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "E:\\device repair manaement system - Copy\\.venv\\Lib\\site-packages\\flask\\app.py", line 1686, in url_for\n    rv = url_adapter.build(  # type: ignore[union-attr]\n         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "E:\\device repair manaement system - Copy\\.venv\\Lib\\site-packages\\werkzeug\\routing\\map.py", line 950, in build\n    raise BuildError(endpoint, values, method, self)\nwerkzeug.routing.exceptions.BuildError: Could not build url for endpoint \'device.get_cost_suggestions\' with values [\'device_id\', \'ticket_id\']. Did you mean \'device_api.get_device_statistics\' instead?\n'}
2025-05-31 18:25:29,495 - device_repair_system - INFO - Error handling system initialized
2025-05-31 18:32:00,024 - device_repair_system - INFO - Error handling system initialized
2025-05-31 18:33:56,325 - device_repair_system - INFO - Error handling system initialized
2025-05-31 18:34:30,819 - device_repair_system - WARNING - Client Error: {'timestamp': '2025-05-31T18:34:30.819782', 'status_code': 404, 'error_type': 'NotFound', 'error_message': '404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.', 'url': 'http://127.0.0.1:5000/devices/1/tickets/21/cost_suggestions', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous'}
2025-05-31 18:34:40,950 - device_repair_system - WARNING - Client Error: {'timestamp': '2025-05-31T18:34:40.949981', 'status_code': 404, 'error_type': 'NotFound', 'error_message': '404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.', 'url': 'http://127.0.0.1:5000/devices/1/tickets/21/cost_suggestions', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous'}
2025-05-31 18:35:04,584 - device_repair_system - WARNING - Client Error: {'timestamp': '2025-05-31T18:35:04.584161', 'status_code': 404, 'error_type': 'NotFound', 'error_message': '404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.', 'url': 'http://127.0.0.1:5000/devices/1/tickets/21/cost_suggestions', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous'}
2025-05-31 18:37:37,518 - device_repair_system - INFO - Error handling system initialized
2025-05-31 18:41:22,846 - device_repair_system - INFO - Error handling system initialized
2025-05-31 18:44:55,715 - device_repair_system - INFO - Error handling system initialized
2025-05-31 18:46:26,472 - device_repair_system - INFO - Error handling system initialized
2025-05-31 18:46:52,521 - device_repair_system - INFO - Error handling system initialized
2025-05-31 18:51:20,825 - device_repair_system - WARNING - Client Error: {'timestamp': '2025-05-31T18:51:20.824523', 'status_code': 400, 'error_type': 'CSRFError', 'error_message': '400 Bad Request: The CSRF token is missing.', 'url': 'http://127.0.0.1:5000/auth/login', 'method': 'POST', 'user_agent': 'python-requests/2.31.0', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous'}
2025-05-31 18:52:14,132 - device_repair_system - INFO - Error handling system initialized
2025-05-31 18:52:15,306 - device_repair_system - INFO - Error handling system initialized
2025-05-31 18:55:05,726 - device_repair_system - INFO - Error handling system initialized
2025-05-31 18:56:45,012 - device_repair_system - INFO - Error handling system initialized
2025-05-31 18:57:12,029 - device_repair_system - INFO - Error handling system initialized
2025-05-31 19:01:12,538 - device_repair_system - INFO - Error handling system initialized
2025-05-31 19:07:05,466 - device_repair_system - INFO - Error handling system initialized
2025-05-31 19:21:13,801 - device_repair_system - INFO - Error handling system initialized
2025-05-31 19:21:53,700 - device_repair_system - INFO - Error handling system initialized
2025-05-31 19:27:43,796 - device_repair_system - INFO - Error handling system initialized
2025-05-31 19:28:46,417 - device_repair_system - INFO - Error handling system initialized
2025-05-31 19:31:15,183 - device_repair_system - INFO - Error handling system initialized
2025-05-31 19:34:53,798 - device_repair_system - INFO - Error handling system initialized
2025-05-31 19:37:30,786 - device_repair_system - INFO - Error handling system initialized
2025-05-31 19:38:46,046 - device_repair_system - INFO - Error handling system initialized
2025-05-31 19:39:51,762 - device_repair_system - INFO - Error handling system initialized
2025-05-31 19:40:03,409 - device_repair_system - WARNING - Client Error: {'timestamp': '2025-05-31T19:40:03.409594', 'status_code': 404, 'error_type': 'NotFound', 'error_message': '404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.', 'url': 'http://127.0.0.1:5000/inventory/add', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous'}
2025-05-31 19:40:03,841 - device_repair_system - WARNING - Client Error: {'timestamp': '2025-05-31T19:40:03.841439', 'status_code': 404, 'error_type': 'NotFound', 'error_message': '404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.', 'url': 'http://127.0.0.1:5000/favicon.ico', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous'}
2025-05-31 19:43:38,805 - device_repair_system - INFO - Error handling system initialized
2025-05-31 19:46:32,831 - device_repair_system - INFO - Error handling system initialized
2025-05-31 19:48:13,530 - device_repair_system - INFO - Error handling system initialized
2025-05-31 19:50:12,754 - device_repair_system - INFO - Error handling system initialized
2025-05-31 20:07:24,899 - device_repair_system - INFO - Error handling system initialized
2025-05-31 20:08:01,955 - device_repair_system - INFO - Error handling system initialized
2025-05-31 20:11:27,836 - device_repair_system - INFO - Error handling system initialized
2025-05-31 20:16:35,678 - device_repair_system - INFO - Error handling system initialized
2025-05-31 20:18:18,400 - device_repair_system - INFO - Error handling system initialized
2025-05-31 20:18:19,620 - device_repair_system - WARNING - Client Error: {'timestamp': '2025-05-31T20:18:19.619564', 'status_code': 400, 'error_type': 'CSRFError', 'error_message': '400 Bad Request: The CSRF token is missing.', 'url': 'http://localhost/auth/login', 'method': 'POST', 'user_agent': 'Werkzeug/2.3.7', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous'}
2025-05-31 20:20:12,742 - device_repair_system - WARNING - Client Error: {'timestamp': '2025-05-31T20:20:12.741330', 'status_code': 404, 'error_type': 'NotFound', 'error_message': '404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.', 'url': 'http://127.0.0.1:5000/inventory/test', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous'}
2025-05-31 20:20:13,120 - device_repair_system - WARNING - Client Error: {'timestamp': '2025-05-31T20:20:13.120318', 'status_code': 404, 'error_type': 'NotFound', 'error_message': '404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.', 'url': 'http://127.0.0.1:5000/favicon.ico', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous'}
2025-05-31 20:20:59,971 - device_repair_system - INFO - Error handling system initialized
2025-05-31 20:22:43,478 - device_repair_system - WARNING - Client Error: {'timestamp': '2025-05-31T20:22:43.478094', 'status_code': 404, 'error_type': 'NotFound', 'error_message': '404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.', 'url': 'http://127.0.0.1:5000/inventory/test', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous'}
2025-05-31 20:27:25,057 - device_repair_system - INFO - Error handling system initialized
2025-05-31 20:30:07,358 - device_repair_system - INFO - Error handling system initialized
2025-05-31 20:33:38,419 - device_repair_system - INFO - Error handling system initialized
2025-05-31 20:38:16,461 - device_repair_system - INFO - Error handling system initialized
2025-05-31 21:07:42,620 - device_repair_system - INFO - Error handling system initialized
2025-05-31 21:16:58,619 - device_repair_system - INFO - Error handling system initialized
2025-05-31 21:41:37,681 - device_repair_system - INFO - Error handling system initialized
2025-05-31 22:03:29,159 - device_repair_system - INFO - Error handling system initialized
2025-05-31 22:13:35,530 - device_repair_system - WARNING - Client Error: {'timestamp': '2025-05-31T22:13:35.529886', 'status_code': 404, 'error_type': 'NotFound', 'error_message': '404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.', 'url': 'http://127.0.0.1:5000/favicon.ico', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous'}
2025-05-31 22:14:45,387 - device_repair_system - ERROR - Server Error: {'timestamp': '2025-05-31T22:14:45.303076', 'status_code': 500, 'error_type': 'BuildError', 'error_message': "Could not build url for endpoint 'whatsapp_test.index'. Did you mean 'users.index' instead?", 'url': 'http://127.0.0.1:5000/dashboard/', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous', 'traceback': 'Traceback (most recent call last):\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1484, in full_dispatch_request\n    rv = self.dispatch_request()\n         ^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1469, in dispatch_request\n    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask_login\\utils.py", line 290, in decorated_view\n    return current_app.ensure_sync(func)(*args, **kwargs)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "E:\\device repair manaement system - Copy\\app\\routes\\dashboard.py", line 50, in index\n    return render_template(\n           ^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\templating.py", line 151, in render_template\n    return _render(app, template, context)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\templating.py", line 132, in _render\n    rv = template.render(context)\n         ^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\environment.py", line 1295, in render\n    self.environment.handle_exception()\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\environment.py", line 942, in handle_exception\n    raise rewrite_traceback_stack(source=source)\n  File "E:\\device repair manaement system - Copy\\app\\templates\\dashboard\\index.html", line 1, in top-level template code\n    {% extends \'base.html\' %}\n  File "E:\\device repair manaement system - Copy\\app\\templates\\base.html", line 441, in top-level template code\n    href="{{ url_for(\'whatsapp_test.index\') }}">\n    ^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1697, in url_for\n    return self.handle_url_build_error(error, endpoint, values)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1686, in url_for\n    rv = url_adapter.build(  # type: ignore[union-attr]\n         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\werkzeug\\routing\\map.py", line 950, in build\n    raise BuildError(endpoint, values, method, self)\nwerkzeug.routing.exceptions.BuildError: Could not build url for endpoint \'whatsapp_test.index\'. Did you mean \'users.index\' instead?\n'}
2025-05-31 22:15:20,637 - device_repair_system - ERROR - Server Error: {'timestamp': '2025-05-31T22:15:20.630047', 'status_code': 500, 'error_type': 'BuildError', 'error_message': "Could not build url for endpoint 'whatsapp_test.index'. Did you mean 'users.index' instead?", 'url': 'http://127.0.0.1:5000/dashboard/', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous', 'traceback': 'Traceback (most recent call last):\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1484, in full_dispatch_request\n    rv = self.dispatch_request()\n         ^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1469, in dispatch_request\n    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask_login\\utils.py", line 290, in decorated_view\n    return current_app.ensure_sync(func)(*args, **kwargs)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "E:\\device repair manaement system - Copy\\app\\routes\\dashboard.py", line 50, in index\n    return render_template(\n           ^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\templating.py", line 151, in render_template\n    return _render(app, template, context)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\templating.py", line 132, in _render\n    rv = template.render(context)\n         ^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\environment.py", line 1295, in render\n    self.environment.handle_exception()\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\environment.py", line 942, in handle_exception\n    raise rewrite_traceback_stack(source=source)\n  File "E:\\device repair manaement system - Copy\\app\\templates\\dashboard\\index.html", line 1, in top-level template code\n    {% extends \'base.html\' %}\n  File "E:\\device repair manaement system - Copy\\app\\templates\\base.html", line 441, in top-level template code\n    href="{{ url_for(\'whatsapp_test.index\') }}">\n    ^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1697, in url_for\n    return self.handle_url_build_error(error, endpoint, values)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1686, in url_for\n    rv = url_adapter.build(  # type: ignore[union-attr]\n         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\werkzeug\\routing\\map.py", line 950, in build\n    raise BuildError(endpoint, values, method, self)\nwerkzeug.routing.exceptions.BuildError: Could not build url for endpoint \'whatsapp_test.index\'. Did you mean \'users.index\' instead?\n'}
2025-05-31 22:16:39,710 - device_repair_system - WARNING - Client Error: {'timestamp': '2025-05-31T22:16:39.710781', 'status_code': 404, 'error_type': 'NotFound', 'error_message': '404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.', 'url': 'http://127.0.0.1:5000/activate', 'method': 'GET', 'user_agent': 'python-requests/2.31.0', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous'}
2025-05-31 22:16:39,989 - device_repair_system - WARNING - Client Error: {'timestamp': '2025-05-31T22:16:39.989994', 'status_code': 404, 'error_type': 'NotFound', 'error_message': '404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.', 'url': 'http://127.0.0.1:5000/whatsapp-test/', 'method': 'GET', 'user_agent': 'python-requests/2.31.0', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous'}
2025-05-31 22:16:40,490 - device_repair_system - WARNING - Client Error: {'timestamp': '2025-05-31T22:16:40.490441', 'status_code': 404, 'error_type': 'NotFound', 'error_message': '404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.', 'url': 'http://127.0.0.1:5000/activate', 'method': 'GET', 'user_agent': 'python-requests/2.31.0', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous'}
2025-05-31 22:16:50,020 - device_repair_system - INFO - Error handling system initialized
2025-05-31 22:17:18,785 - device_repair_system - WARNING - Client Error: {'timestamp': '2025-05-31T22:17:18.785251', 'status_code': 404, 'error_type': 'NotFound', 'error_message': '404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.', 'url': 'http://127.0.0.1:5000/activate', 'method': 'GET', 'user_agent': 'python-requests/2.31.0', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous'}
2025-05-31 22:17:19,204 - device_repair_system - WARNING - Client Error: {'timestamp': '2025-05-31T22:17:19.204156', 'status_code': 404, 'error_type': 'NotFound', 'error_message': '404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.', 'url': 'http://127.0.0.1:5000/whatsapp-test/', 'method': 'GET', 'user_agent': 'python-requests/2.31.0', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous'}
2025-05-31 22:17:19,653 - device_repair_system - WARNING - Client Error: {'timestamp': '2025-05-31T22:17:19.653805', 'status_code': 404, 'error_type': 'NotFound', 'error_message': '404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.', 'url': 'http://127.0.0.1:5000/activate', 'method': 'GET', 'user_agent': 'python-requests/2.31.0', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous'}
2025-05-31 22:17:52,194 - device_repair_system - INFO - Error handling system initialized
2025-05-31 22:18:11,171 - device_repair_system - INFO - Error handling system initialized
2025-05-31 22:18:25,806 - device_repair_system - ERROR - Server Error: {'timestamp': '2025-05-31T22:18:25.799577', 'status_code': 500, 'error_type': 'BuildError', 'error_message': "Could not build url for endpoint 'whatsapp_test.index'. Did you mean 'users.index' instead?", 'url': 'http://127.0.0.1:5000/dashboard/', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous', 'traceback': 'Traceback (most recent call last):\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1484, in full_dispatch_request\n    rv = self.dispatch_request()\n         ^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1469, in dispatch_request\n    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask_login\\utils.py", line 290, in decorated_view\n    return current_app.ensure_sync(func)(*args, **kwargs)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "E:\\device repair manaement system - Copy\\app\\routes\\dashboard.py", line 50, in index\n    return render_template(\n           ^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\templating.py", line 151, in render_template\n    return _render(app, template, context)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\templating.py", line 132, in _render\n    rv = template.render(context)\n         ^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\environment.py", line 1295, in render\n    self.environment.handle_exception()\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\environment.py", line 942, in handle_exception\n    raise rewrite_traceback_stack(source=source)\n  File "E:\\device repair manaement system - Copy\\app\\templates\\dashboard\\index.html", line 1, in top-level template code\n    {% extends \'base.html\' %}\n  File "E:\\device repair manaement system - Copy\\app\\templates\\base.html", line 441, in top-level template code\n    href="{{ url_for(\'whatsapp_test.index\') }}">\n    ^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1697, in url_for\n    return self.handle_url_build_error(error, endpoint, values)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1686, in url_for\n    rv = url_adapter.build(  # type: ignore[union-attr]\n         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\werkzeug\\routing\\map.py", line 950, in build\n    raise BuildError(endpoint, values, method, self)\nwerkzeug.routing.exceptions.BuildError: Could not build url for endpoint \'whatsapp_test.index\'. Did you mean \'users.index\' instead?\n'}
2025-05-31 22:18:37,518 - device_repair_system - WARNING - Client Error: {'timestamp': '2025-05-31T22:18:37.518566', 'status_code': 404, 'error_type': 'NotFound', 'error_message': '404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.', 'url': 'http://127.0.0.1:5000/activate', 'method': 'GET', 'user_agent': 'python-requests/2.31.0', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous'}
2025-05-31 22:18:37,897 - device_repair_system - WARNING - Client Error: {'timestamp': '2025-05-31T22:18:37.897480', 'status_code': 404, 'error_type': 'NotFound', 'error_message': '404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.', 'url': 'http://127.0.0.1:5000/whatsapp-test/', 'method': 'GET', 'user_agent': 'python-requests/2.31.0', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous'}
2025-05-31 22:18:38,492 - device_repair_system - WARNING - Client Error: {'timestamp': '2025-05-31T22:18:38.492177', 'status_code': 404, 'error_type': 'NotFound', 'error_message': '404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.', 'url': 'http://127.0.0.1:5000/activate', 'method': 'GET', 'user_agent': 'python-requests/2.31.0', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous'}
2025-05-31 22:18:47,518 - device_repair_system - WARNING - Client Error: {'timestamp': '2025-05-31T22:18:47.518892', 'status_code': 404, 'error_type': 'NotFound', 'error_message': '404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.', 'url': 'http://127.0.0.1:5000/activate', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.19041.5915', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous'}
2025-05-31 22:19:48,181 - device_repair_system - INFO - Error handling system initialized
2025-05-31 22:19:49,399 - device_repair_system - INFO - Error handling system initialized
2025-05-31 22:19:59,895 - device_repair_system - ERROR - Server Error: {'timestamp': '2025-05-31T22:19:59.888961', 'status_code': 500, 'error_type': 'BuildError', 'error_message': "Could not build url for endpoint 'whatsapp_test.index'. Did you mean 'users.index' instead?", 'url': 'http://127.0.0.1:5000/notifications/user/preferences', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous', 'traceback': 'Traceback (most recent call last):\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1484, in full_dispatch_request\n    rv = self.dispatch_request()\n         ^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1469, in dispatch_request\n    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask_login\\utils.py", line 290, in decorated_view\n    return current_app.ensure_sync(func)(*args, **kwargs)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "E:\\device repair manaement system - Copy\\app\\routes\\notifications.py", line 139, in user_preferences\n    return render_template(\'notifications/user_preferences.html\', user_prefs=user_prefs)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\templating.py", line 151, in render_template\n    return _render(app, template, context)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\templating.py", line 132, in _render\n    rv = template.render(context)\n         ^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\environment.py", line 1295, in render\n    self.environment.handle_exception()\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\environment.py", line 942, in handle_exception\n    raise rewrite_traceback_stack(source=source)\n  File "E:\\device repair manaement system - Copy\\app\\templates\\notifications\\user_preferences.html", line 1, in top-level template code\n    {% extends "base.html" %}\n  File "E:\\device repair manaement system - Copy\\app\\templates\\base.html", line 441, in top-level template code\n    href="{{ url_for(\'whatsapp_test.index\') }}">\n    ^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1697, in url_for\n    return self.handle_url_build_error(error, endpoint, values)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1686, in url_for\n    rv = url_adapter.build(  # type: ignore[union-attr]\n         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\werkzeug\\routing\\map.py", line 950, in build\n    raise BuildError(endpoint, values, method, self)\nwerkzeug.routing.exceptions.BuildError: Could not build url for endpoint \'whatsapp_test.index\'. Did you mean \'users.index\' instead?\n'}
2025-05-31 22:20:01,317 - device_repair_system - INFO - Error handling system initialized
2025-05-31 22:20:06,391 - device_repair_system - ERROR - Server Error: {'timestamp': '2025-05-31T22:20:06.381673', 'status_code': 500, 'error_type': 'BuildError', 'error_message': "Could not build url for endpoint 'whatsapp_test.index'. Did you mean 'users.index' instead?", 'url': 'http://127.0.0.1:5000/dashboard/', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous', 'traceback': 'Traceback (most recent call last):\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1484, in full_dispatch_request\n    rv = self.dispatch_request()\n         ^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1469, in dispatch_request\n    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask_login\\utils.py", line 290, in decorated_view\n    return current_app.ensure_sync(func)(*args, **kwargs)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "E:\\device repair manaement system - Copy\\app\\routes\\dashboard.py", line 50, in index\n    return render_template(\n           ^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\templating.py", line 151, in render_template\n    return _render(app, template, context)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\templating.py", line 132, in _render\n    rv = template.render(context)\n         ^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\environment.py", line 1295, in render\n    self.environment.handle_exception()\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\environment.py", line 942, in handle_exception\n    raise rewrite_traceback_stack(source=source)\n  File "E:\\device repair manaement system - Copy\\app\\templates\\dashboard\\index.html", line 1, in top-level template code\n    {% extends \'base.html\' %}\n  File "E:\\device repair manaement system - Copy\\app\\templates\\base.html", line 441, in top-level template code\n    href="{{ url_for(\'whatsapp_test.index\') }}">\n    ^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1697, in url_for\n    return self.handle_url_build_error(error, endpoint, values)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1686, in url_for\n    rv = url_adapter.build(  # type: ignore[union-attr]\n         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\werkzeug\\routing\\map.py", line 950, in build\n    raise BuildError(endpoint, values, method, self)\nwerkzeug.routing.exceptions.BuildError: Could not build url for endpoint \'whatsapp_test.index\'. Did you mean \'users.index\' instead?\n'}
2025-05-31 22:20:07,653 - device_repair_system - ERROR - Server Error: {'timestamp': '2025-05-31T22:20:07.644220', 'status_code': 500, 'error_type': 'BuildError', 'error_message': "Could not build url for endpoint 'whatsapp_test.index'. Did you mean 'users.index' instead?", 'url': 'http://127.0.0.1:5000/dashboard/', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous', 'traceback': 'Traceback (most recent call last):\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1484, in full_dispatch_request\n    rv = self.dispatch_request()\n         ^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1469, in dispatch_request\n    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask_login\\utils.py", line 290, in decorated_view\n    return current_app.ensure_sync(func)(*args, **kwargs)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "E:\\device repair manaement system - Copy\\app\\routes\\dashboard.py", line 50, in index\n    return render_template(\n           ^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\templating.py", line 151, in render_template\n    return _render(app, template, context)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\templating.py", line 132, in _render\n    rv = template.render(context)\n         ^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\environment.py", line 1295, in render\n    self.environment.handle_exception()\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\environment.py", line 942, in handle_exception\n    raise rewrite_traceback_stack(source=source)\n  File "E:\\device repair manaement system - Copy\\app\\templates\\dashboard\\index.html", line 1, in top-level template code\n    {% extends \'base.html\' %}\n  File "E:\\device repair manaement system - Copy\\app\\templates\\base.html", line 441, in top-level template code\n    href="{{ url_for(\'whatsapp_test.index\') }}">\n    ^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1697, in url_for\n    return self.handle_url_build_error(error, endpoint, values)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1686, in url_for\n    rv = url_adapter.build(  # type: ignore[union-attr]\n         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\werkzeug\\routing\\map.py", line 950, in build\n    raise BuildError(endpoint, values, method, self)\nwerkzeug.routing.exceptions.BuildError: Could not build url for endpoint \'whatsapp_test.index\'. Did you mean \'users.index\' instead?\n'}
2025-05-31 22:20:25,395 - device_repair_system - ERROR - Server Error: {'timestamp': '2025-05-31T22:20:25.390486', 'status_code': 500, 'error_type': 'BuildError', 'error_message': "Could not build url for endpoint 'whatsapp_test.index'. Did you mean 'users.index' instead?", 'url': 'http://127.0.0.1:5000/dashboard/', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous', 'traceback': 'Traceback (most recent call last):\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1484, in full_dispatch_request\n    rv = self.dispatch_request()\n         ^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1469, in dispatch_request\n    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask_login\\utils.py", line 290, in decorated_view\n    return current_app.ensure_sync(func)(*args, **kwargs)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "E:\\device repair manaement system - Copy\\app\\routes\\dashboard.py", line 50, in index\n    return render_template(\n           ^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\templating.py", line 151, in render_template\n    return _render(app, template, context)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\templating.py", line 132, in _render\n    rv = template.render(context)\n         ^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\environment.py", line 1295, in render\n    self.environment.handle_exception()\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\environment.py", line 942, in handle_exception\n    raise rewrite_traceback_stack(source=source)\n  File "E:\\device repair manaement system - Copy\\app\\templates\\dashboard\\index.html", line 1, in top-level template code\n    {% extends \'base.html\' %}\n  File "E:\\device repair manaement system - Copy\\app\\templates\\base.html", line 441, in top-level template code\n    href="{{ url_for(\'whatsapp_test.index\') }}">\n    ^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1697, in url_for\n    return self.handle_url_build_error(error, endpoint, values)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1686, in url_for\n    rv = url_adapter.build(  # type: ignore[union-attr]\n         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\werkzeug\\routing\\map.py", line 950, in build\n    raise BuildError(endpoint, values, method, self)\nwerkzeug.routing.exceptions.BuildError: Could not build url for endpoint \'whatsapp_test.index\'. Did you mean \'users.index\' instead?\n'}
2025-05-31 22:20:28,263 - device_repair_system - WARNING - Client Error: {'timestamp': '2025-05-31T22:20:28.262773', 'status_code': 404, 'error_type': 'NotFound', 'error_message': '404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.', 'url': 'http://127.0.0.1:5000/activate', 'method': 'GET', 'user_agent': 'python-requests/2.31.0', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous'}
2025-05-31 22:20:28,722 - device_repair_system - WARNING - Client Error: {'timestamp': '2025-05-31T22:20:28.722141', 'status_code': 404, 'error_type': 'NotFound', 'error_message': '404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.', 'url': 'http://127.0.0.1:5000/whatsapp-test/', 'method': 'GET', 'user_agent': 'python-requests/2.31.0', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous'}
2025-05-31 22:20:29,207 - device_repair_system - WARNING - Client Error: {'timestamp': '2025-05-31T22:20:29.207837', 'status_code': 404, 'error_type': 'NotFound', 'error_message': '404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.', 'url': 'http://127.0.0.1:5000/activate', 'method': 'GET', 'user_agent': 'python-requests/2.31.0', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous'}
2025-05-31 22:20:37,101 - device_repair_system - ERROR - Server Error: {'timestamp': '2025-05-31T22:20:37.088795', 'status_code': 500, 'error_type': 'BuildError', 'error_message': "Could not build url for endpoint 'whatsapp_test.index'. Did you mean 'users.index' instead?", 'url': 'http://127.0.0.1:5000/devices/', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous', 'traceback': 'Traceback (most recent call last):\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1484, in full_dispatch_request\n    rv = self.dispatch_request()\n         ^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1469, in dispatch_request\n    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask_login\\utils.py", line 290, in decorated_view\n    return current_app.ensure_sync(func)(*args, **kwargs)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "E:\\device repair manaement system - Copy\\app\\routes\\device.py", line 98, in index\n    return render_template(\'device/index.html\', devices=devices, dashboard_stats=dashboard_stats)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\templating.py", line 151, in render_template\n    return _render(app, template, context)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\templating.py", line 132, in _render\n    rv = template.render(context)\n         ^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\environment.py", line 1295, in render\n    self.environment.handle_exception()\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\environment.py", line 942, in handle_exception\n    raise rewrite_traceback_stack(source=source)\n  File "E:\\device repair manaement system - Copy\\app\\templates\\device\\index.html", line 1, in top-level template code\n    {% extends \'base.html\' %}\n  File "E:\\device repair manaement system - Copy\\app\\templates\\base.html", line 441, in top-level template code\n    href="{{ url_for(\'whatsapp_test.index\') }}">\n    ^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1697, in url_for\n    return self.handle_url_build_error(error, endpoint, values)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1686, in url_for\n    rv = url_adapter.build(  # type: ignore[union-attr]\n         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\werkzeug\\routing\\map.py", line 950, in build\n    raise BuildError(endpoint, values, method, self)\nwerkzeug.routing.exceptions.BuildError: Could not build url for endpoint \'whatsapp_test.index\'. Did you mean \'users.index\' instead?\n'}
2025-05-31 22:20:37,174 - device_repair_system - WARNING - Client Error: {'timestamp': '2025-05-31T22:20:37.174763', 'status_code': 404, 'error_type': 'NotFound', 'error_message': '404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.', 'url': 'http://127.0.0.1:5000/activate', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.19041.5915', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous'}
2025-05-31 22:21:12,873 - device_repair_system - INFO - Error handling system initialized
2025-05-31 22:21:33,456 - device_repair_system - ERROR - Server Error: {'timestamp': '2025-05-31T22:21:33.449075', 'status_code': 500, 'error_type': 'BuildError', 'error_message': "Could not build url for endpoint 'whatsapp_test.index'. Did you mean 'users.index' instead?", 'url': 'http://127.0.0.1:5000/dashboard/', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous', 'traceback': 'Traceback (most recent call last):\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1484, in full_dispatch_request\n    rv = self.dispatch_request()\n         ^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1469, in dispatch_request\n    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask_login\\utils.py", line 290, in decorated_view\n    return current_app.ensure_sync(func)(*args, **kwargs)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "E:\\device repair manaement system - Copy\\app\\routes\\dashboard.py", line 50, in index\n    return render_template(\n           ^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\templating.py", line 151, in render_template\n    return _render(app, template, context)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\templating.py", line 132, in _render\n    rv = template.render(context)\n         ^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\environment.py", line 1295, in render\n    self.environment.handle_exception()\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\environment.py", line 942, in handle_exception\n    raise rewrite_traceback_stack(source=source)\n  File "E:\\device repair manaement system - Copy\\app\\templates\\dashboard\\index.html", line 1, in top-level template code\n    {% extends \'base.html\' %}\n  File "E:\\device repair manaement system - Copy\\app\\templates\\base.html", line 441, in top-level template code\n    href="{{ url_for(\'whatsapp_test.index\') }}">\n    ^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1697, in url_for\n    return self.handle_url_build_error(error, endpoint, values)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1686, in url_for\n    rv = url_adapter.build(  # type: ignore[union-attr]\n         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\werkzeug\\routing\\map.py", line 950, in build\n    raise BuildError(endpoint, values, method, self)\nwerkzeug.routing.exceptions.BuildError: Could not build url for endpoint \'whatsapp_test.index\'. Did you mean \'users.index\' instead?\n'}
2025-05-31 22:21:39,324 - device_repair_system - WARNING - Client Error: {'timestamp': '2025-05-31T22:21:39.324575', 'status_code': 404, 'error_type': 'NotFound', 'error_message': '404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.', 'url': 'http://127.0.0.1:5000/activate', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.19041.5915', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous'}
2025-05-31 22:22:22,580 - device_repair_system - INFO - Error handling system initialized
2025-05-31 22:24:21,999 - device_repair_system - ERROR - Server Error: {'timestamp': '2025-05-31T22:24:21.991333', 'status_code': 500, 'error_type': 'BuildError', 'error_message': "Could not build url for endpoint 'whatsapp_test.index'. Did you mean 'users.index' instead?", 'url': 'http://127.0.0.1:5000/technician/', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous', 'traceback': 'Traceback (most recent call last):\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1484, in full_dispatch_request\n    rv = self.dispatch_request()\n         ^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1469, in dispatch_request\n    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask_login\\utils.py", line 290, in decorated_view\n    return current_app.ensure_sync(func)(*args, **kwargs)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "E:\\device repair manaement system - Copy\\app\\routes\\technician.py", line 19, in index\n    return render_template(\'technician/index.html\', technicians=technicians)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\templating.py", line 151, in render_template\n    return _render(app, template, context)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\templating.py", line 132, in _render\n    rv = template.render(context)\n         ^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\environment.py", line 1295, in render\n    self.environment.handle_exception()\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\environment.py", line 942, in handle_exception\n    raise rewrite_traceback_stack(source=source)\n  File "E:\\device repair manaement system - Copy\\app\\templates\\technician\\index.html", line 1, in top-level template code\n    {% extends \'base.html\' %}\n  File "E:\\device repair manaement system - Copy\\app\\templates\\base.html", line 441, in top-level template code\n    href="{{ url_for(\'whatsapp_test.index\') }}">\n    ^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1697, in url_for\n    return self.handle_url_build_error(error, endpoint, values)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1686, in url_for\n    rv = url_adapter.build(  # type: ignore[union-attr]\n         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\werkzeug\\routing\\map.py", line 950, in build\n    raise BuildError(endpoint, values, method, self)\nwerkzeug.routing.exceptions.BuildError: Could not build url for endpoint \'whatsapp_test.index\'. Did you mean \'users.index\' instead?\n'}
2025-05-31 22:27:04,621 - device_repair_system - INFO - Error handling system initialized
2025-05-31 22:33:15,640 - device_repair_system - WARNING - Client Error: {'timestamp': '2025-05-31T22:33:15.640603', 'status_code': 405, 'error_type': 'MethodNotAllowed', 'error_message': '405 Method Not Allowed: The method is not allowed for the requested URL.', 'url': 'http://127.0.0.1:5000/inventory/323/delete', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous'}
2025-05-31 22:33:27,390 - device_repair_system - WARNING - Client Error: {'timestamp': '2025-05-31T22:33:27.389659', 'status_code': 405, 'error_type': 'MethodNotAllowed', 'error_message': '405 Method Not Allowed: The method is not allowed for the requested URL.', 'url': 'http://127.0.0.1:5000/inventory/323/delete', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous'}
2025-05-31 22:33:47,186 - device_repair_system - WARNING - Client Error: {'timestamp': '2025-05-31T22:33:47.186124', 'status_code': 405, 'error_type': 'MethodNotAllowed', 'error_message': '405 Method Not Allowed: The method is not allowed for the requested URL.', 'url': 'http://127.0.0.1:5000/inventory/323/delete', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous'}
2025-05-31 22:37:57,299 - device_repair_system - INFO - Error handling system initialized
2025-05-31 22:38:12,003 - device_repair_system - WARNING - Client Error: {'timestamp': '2025-05-31T22:38:12.003544', 'status_code': 404, 'error_type': 'NotFound', 'error_message': '404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.', 'url': 'http://127.0.0.1:5000/promo', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous'}
2025-05-31 22:49:05,870 - device_repair_system - INFO - Error handling system initialized
2025-05-31 22:49:06,927 - device_repair_system - INFO - Error handling system initialized
2025-05-31 22:49:07,009 - device_repair_system - INFO - Error handling system initialized
2025-05-31 22:49:07,083 - device_repair_system - INFO - Error handling system initialized
2025-05-31 22:49:07,367 - device_repair_system - INFO - Error handling system initialized
2025-05-31 22:49:07,476 - device_repair_system - INFO - Error handling system initialized
2025-05-31 23:02:28,579 - device_repair_system - INFO - Error handling system initialized
2025-05-31 23:02:29,611 - device_repair_system - INFO - Error handling system initialized
2025-05-31 23:02:29,617 - device_repair_system - INFO - Error handling system initialized
2025-05-31 23:02:29,623 - device_repair_system - INFO - Error handling system initialized
2025-05-31 23:02:29,629 - device_repair_system - INFO - Error handling system initialized
2025-05-31 23:02:29,635 - device_repair_system - INFO - Error handling system initialized
2025-05-31 23:04:10,340 - device_repair_system - INFO - Error handling system initialized
2025-05-31 23:04:11,286 - device_repair_system - INFO - Error handling system initialized
2025-05-31 23:09:27,479 - device_repair_system - INFO - Error handling system initialized
2025-05-31 23:13:35,383 - device_repair_system - INFO - Error handling system initialized
2025-05-31 23:14:44,997 - device_repair_system - INFO - Error handling system initialized
2025-05-31 23:14:47,963 - device_repair_system - INFO - Error handling system initialized
2025-05-31 23:14:48,099 - device_repair_system - INFO - Error handling system initialized
2025-05-31 23:14:48,228 - device_repair_system - INFO - Error handling system initialized
2025-05-31 23:16:03,518 - device_repair_system - INFO - Error handling system initialized
2025-05-31 23:16:12,324 - device_repair_system - INFO - Error handling system initialized
2025-05-31 23:16:12,449 - device_repair_system - INFO - Error handling system initialized
2025-05-31 23:16:12,577 - device_repair_system - INFO - Error handling system initialized
2025-06-01 12:11:07,756 - device_repair_system - INFO - Error handling system initialized
2025-06-01 12:11:09,672 - device_repair_system - INFO - Error handling system initialized
2025-06-01 12:11:09,928 - device_repair_system - INFO - Error handling system initialized
2025-06-01 12:11:10,056 - device_repair_system - INFO - Error handling system initialized
2025-06-01 12:11:36,600 - device_repair_system - INFO - Error handling system initialized
2025-06-01 12:11:45,998 - device_repair_system - INFO - Error handling system initialized
2025-06-01 12:11:46,122 - device_repair_system - INFO - Error handling system initialized
2025-06-01 12:11:46,237 - device_repair_system - INFO - Error handling system initialized
2025-06-01 12:26:50,032 - device_repair_system - INFO - Error handling system initialized
2025-06-01 12:26:57,656 - device_repair_system - INFO - Error handling system initialized
2025-06-01 12:26:57,837 - device_repair_system - INFO - Error handling system initialized
2025-06-01 12:26:57,960 - device_repair_system - INFO - Error handling system initialized
2025-06-01 12:46:44,683 - device_repair_system - INFO - Error handling system initialized
2025-06-01 13:03:00,683 - device_repair_system - INFO - Error handling system initialized
2025-06-01 13:03:01,951 - device_repair_system - INFO - Error handling system initialized
2025-06-01 13:03:02,037 - device_repair_system - INFO - Error handling system initialized
2025-06-01 13:03:02,169 - device_repair_system - INFO - Error handling system initialized
2025-06-01 13:03:02,264 - device_repair_system - INFO - Error handling system initialized
2025-06-01 13:04:35,373 - device_repair_system - INFO - Error handling system initialized
2025-06-01 13:05:02,633 - device_repair_system - WARNING - Client Error: {'timestamp': '2025-06-01T13:05:02.633733', 'status_code': 404, 'error_type': 'NotFound', 'error_message': '404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.', 'url': 'http://127.0.0.1:5000/favicon.ico', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous'}
2025-06-01 13:17:25,840 - device_repair_system - INFO - Error handling system initialized
2025-06-01 13:17:30,462 - device_repair_system - INFO - Error handling system initialized
2025-06-01 13:17:32,161 - device_repair_system - INFO - Error handling system initialized
2025-06-01 13:17:32,939 - device_repair_system - INFO - Error handling system initialized
2025-06-01 13:17:33,042 - device_repair_system - WARNING - Client Error: {'timestamp': '2025-06-01T13:17:33.042837', 'status_code': 400, 'error_type': 'CSRFError', 'error_message': '400 Bad Request: The CSRF token is missing.', 'url': 'http://localhost/support/start-chat', 'method': 'POST', 'user_agent': 'Werkzeug/2.3.7', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous'}
2025-06-01 13:17:33,283 - device_repair_system - INFO - Error handling system initialized
2025-06-01 13:20:08,804 - device_repair_system - INFO - Error handling system initialized
2025-06-01 13:20:10,030 - device_repair_system - INFO - Error handling system initialized
2025-06-01 13:20:10,848 - device_repair_system - INFO - Error handling system initialized
2025-06-01 13:20:31,111 - device_repair_system - INFO - Error handling system initialized
2025-06-01 13:25:39,534 - device_repair_system - INFO - Error handling system initialized
2025-06-01 13:25:40,668 - device_repair_system - INFO - Error handling system initialized
2025-06-01 13:26:16,449 - device_repair_system - INFO - Error handling system initialized
2025-06-01 13:28:53,331 - device_repair_system - INFO - Error handling system initialized
2025-06-01 13:28:54,411 - device_repair_system - INFO - Error handling system initialized
2025-06-01 13:36:02,359 - device_repair_system - INFO - Error handling system initialized
2025-06-01 13:36:04,014 - device_repair_system - INFO - Error handling system initialized
2025-06-01 13:36:04,095 - device_repair_system - INFO - Error handling system initialized
2025-06-01 13:36:04,187 - device_repair_system - WARNING - Client Error: {'timestamp': '2025-06-01T13:36:04.187380', 'status_code': 400, 'error_type': 'CSRFError', 'error_message': '400 Bad Request: The CSRF token is missing.', 'url': 'http://localhost/api/telegram/customer/link-customer', 'method': 'POST', 'user_agent': 'Werkzeug/2.3.7', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous'}
2025-06-01 13:36:04,419 - device_repair_system - WARNING - Client Error: {'timestamp': '2025-06-01T13:36:04.419554', 'status_code': 400, 'error_type': 'CSRFError', 'error_message': '400 Bad Request: The CSRF token is missing.', 'url': 'http://localhost/api/telegram/customer/send-status-update', 'method': 'POST', 'user_agent': 'Werkzeug/2.3.7', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous'}
2025-06-01 13:36:04,635 - device_repair_system - WARNING - Client Error: {'timestamp': '2025-06-01T13:36:04.634835', 'status_code': 400, 'error_type': 'CSRFError', 'error_message': '400 Bad Request: The CSRF token is missing.', 'url': 'http://localhost/api/telegram/customer/test-notification', 'method': 'POST', 'user_agent': 'Werkzeug/2.3.7', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous'}
2025-06-01 13:36:04,840 - device_repair_system - INFO - Error handling system initialized
2025-06-01 13:36:04,935 - device_repair_system - WARNING - Client Error: {'timestamp': '2025-06-01T13:36:04.935166', 'status_code': 400, 'error_type': 'CSRFError', 'error_message': '400 Bad Request: The CSRF token is missing.', 'url': 'http://localhost/api/telegram/customer/webhook', 'method': 'POST', 'user_agent': 'Werkzeug/2.3.7', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous'}
2025-06-01 13:36:05,210 - device_repair_system - WARNING - Client Error: {'timestamp': '2025-06-01T13:36:05.210818', 'status_code': 400, 'error_type': 'CSRFError', 'error_message': '400 Bad Request: The CSRF token is missing.', 'url': 'http://localhost/api/telegram/customer/webhook', 'method': 'POST', 'user_agent': 'Werkzeug/2.3.7', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous'}
2025-06-01 13:39:21,412 - device_repair_system - INFO - Error handling system initialized
2025-06-01 13:39:30,486 - device_repair_system - WARNING - Client Error: {'timestamp': '2025-06-01T13:39:30.486725', 'status_code': 404, 'error_type': 'NotFound', 'error_message': '404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.', 'url': 'http://localhost:5000/favicon.ico', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous'}
2025-06-01 13:44:32,807 - device_repair_system - ERROR - Server Error: {'timestamp': '2025-06-01T13:44:32.669081', 'status_code': 500, 'error_type': 'InvalidRequestError', 'error_message': "One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[Customer(customer)]'. Original exception was: Could not determine join condition between parent/child tables on relationship Customer.devices - there are no foreign keys linking these tables.  Ensure that referencing columns are associated with a ForeignKey or ForeignKeyConstraint, or specify a 'primaryjoin' expression.", 'url': 'http://localhost:5000/auth/register', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous', 'traceback': 'Traceback (most recent call last):\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1484, in full_dispatch_request\n    rv = self.dispatch_request()\n         ^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1469, in dispatch_request\n    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "E:\\device repair manaement system - Copy\\app\\routes\\auth.py", line 80, in register\n    if User.query.count() > 0:\n       ^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask_sqlalchemy\\model.py", line 30, in __get__\n    return cls.query_class(\n           ^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\sqlalchemy\\orm\\query.py", line 276, in __init__\n    self._set_entities(entities)\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\sqlalchemy\\orm\\query.py", line 288, in _set_entities\n    self._raw_columns = [\n                        ^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\sqlalchemy\\orm\\query.py", line 289, in <listcomp>\n    coercions.expect(\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\sqlalchemy\\sql\\coercions.py", line 406, in expect\n    insp._post_inspect\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\sqlalchemy\\util\\langhelpers.py", line 1260, in __get__\n    obj.__dict__[self.__name__] = result = self.fget(obj)\n                                           ^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\sqlalchemy\\orm\\mapper.py", line 2707, in _post_inspect\n    self._check_configure()\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\sqlalchemy\\orm\\mapper.py", line 2386, in _check_configure\n    _configure_registries({self.registry}, cascade=True)\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\sqlalchemy\\orm\\mapper.py", line 4199, in _configure_registries\n    _do_configure_registries(registries, cascade)\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\sqlalchemy\\orm\\mapper.py", line 4236, in _do_configure_registries\n    raise e\nsqlalchemy.exc.InvalidRequestError: One or more mappers failed to initialize - can\'t proceed with initialization of other mappers. Triggering mapper: \'Mapper[Customer(customer)]\'. Original exception was: Could not determine join condition between parent/child tables on relationship Customer.devices - there are no foreign keys linking these tables.  Ensure that referencing columns are associated with a ForeignKey or ForeignKeyConstraint, or specify a \'primaryjoin\' expression.\n'}
2025-06-01 13:45:16,718 - device_repair_system - ERROR - Server Error: {'timestamp': '2025-06-01T13:45:16.716846', 'status_code': 500, 'error_type': 'InvalidRequestError', 'error_message': "One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[Customer(customer)]'. Original exception was: Could not determine join condition between parent/child tables on relationship Customer.devices - there are no foreign keys linking these tables.  Ensure that referencing columns are associated with a ForeignKey or ForeignKeyConstraint, or specify a 'primaryjoin' expression.", 'url': 'http://localhost:5000/auth/login', 'method': 'POST', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous', 'traceback': 'Traceback (most recent call last):\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1484, in full_dispatch_request\n    rv = self.dispatch_request()\n         ^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1469, in dispatch_request\n    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "E:\\device repair manaement system - Copy\\app\\routes\\auth.py", line 27, in login\n    user = User.query.filter_by(username=username).first()\n           ^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask_sqlalchemy\\model.py", line 30, in __get__\n    return cls.query_class(\n           ^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\sqlalchemy\\orm\\query.py", line 276, in __init__\n    self._set_entities(entities)\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\sqlalchemy\\orm\\query.py", line 288, in _set_entities\n    self._raw_columns = [\n                        ^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\sqlalchemy\\orm\\query.py", line 289, in <listcomp>\n    coercions.expect(\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\sqlalchemy\\sql\\coercions.py", line 406, in expect\n    insp._post_inspect\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\sqlalchemy\\util\\langhelpers.py", line 1260, in __get__\n    obj.__dict__[self.__name__] = result = self.fget(obj)\n                                           ^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\sqlalchemy\\orm\\mapper.py", line 2707, in _post_inspect\n    self._check_configure()\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\sqlalchemy\\orm\\mapper.py", line 2386, in _check_configure\n    _configure_registries({self.registry}, cascade=True)\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\sqlalchemy\\orm\\mapper.py", line 4199, in _configure_registries\n    _do_configure_registries(registries, cascade)\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\sqlalchemy\\orm\\mapper.py", line 4236, in _do_configure_registries\n    raise e\nsqlalchemy.exc.InvalidRequestError: One or more mappers failed to initialize - can\'t proceed with initialization of other mappers. Triggering mapper: \'Mapper[Customer(customer)]\'. Original exception was: Could not determine join condition between parent/child tables on relationship Customer.devices - there are no foreign keys linking these tables.  Ensure that referencing columns are associated with a ForeignKey or ForeignKeyConstraint, or specify a \'primaryjoin\' expression.\n'}
2025-06-01 13:45:30,611 - device_repair_system - ERROR - Server Error: {'timestamp': '2025-06-01T13:45:30.610393', 'status_code': 500, 'error_type': 'InvalidRequestError', 'error_message': "One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[Customer(customer)]'. Original exception was: Could not determine join condition between parent/child tables on relationship Customer.devices - there are no foreign keys linking these tables.  Ensure that referencing columns are associated with a ForeignKey or ForeignKeyConstraint, or specify a 'primaryjoin' expression.", 'url': 'http://localhost:5000/auth/login', 'method': 'POST', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous', 'traceback': 'Traceback (most recent call last):\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1484, in full_dispatch_request\n    rv = self.dispatch_request()\n         ^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1469, in dispatch_request\n    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "E:\\device repair manaement system - Copy\\app\\routes\\auth.py", line 27, in login\n    user = User.query.filter_by(username=username).first()\n           ^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask_sqlalchemy\\model.py", line 30, in __get__\n    return cls.query_class(\n           ^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\sqlalchemy\\orm\\query.py", line 276, in __init__\n    self._set_entities(entities)\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\sqlalchemy\\orm\\query.py", line 288, in _set_entities\n    self._raw_columns = [\n                        ^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\sqlalchemy\\orm\\query.py", line 289, in <listcomp>\n    coercions.expect(\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\sqlalchemy\\sql\\coercions.py", line 406, in expect\n    insp._post_inspect\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\sqlalchemy\\util\\langhelpers.py", line 1260, in __get__\n    obj.__dict__[self.__name__] = result = self.fget(obj)\n                                           ^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\sqlalchemy\\orm\\mapper.py", line 2707, in _post_inspect\n    self._check_configure()\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\sqlalchemy\\orm\\mapper.py", line 2386, in _check_configure\n    _configure_registries({self.registry}, cascade=True)\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\sqlalchemy\\orm\\mapper.py", line 4199, in _configure_registries\n    _do_configure_registries(registries, cascade)\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\sqlalchemy\\orm\\mapper.py", line 4236, in _do_configure_registries\n    raise e\nsqlalchemy.exc.InvalidRequestError: One or more mappers failed to initialize - can\'t proceed with initialization of other mappers. Triggering mapper: \'Mapper[Customer(customer)]\'. Original exception was: Could not determine join condition between parent/child tables on relationship Customer.devices - there are no foreign keys linking these tables.  Ensure that referencing columns are associated with a ForeignKey or ForeignKeyConstraint, or specify a \'primaryjoin\' expression.\n'}
2025-06-01 13:45:34,077 - device_repair_system - ERROR - Server Error: {'timestamp': '2025-06-01T13:45:34.076035', 'status_code': 500, 'error_type': 'InvalidRequestError', 'error_message': "One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[Customer(customer)]'. Original exception was: Could not determine join condition between parent/child tables on relationship Customer.devices - there are no foreign keys linking these tables.  Ensure that referencing columns are associated with a ForeignKey or ForeignKeyConstraint, or specify a 'primaryjoin' expression.", 'url': 'http://localhost:5000/auth/register', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous', 'traceback': 'Traceback (most recent call last):\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1484, in full_dispatch_request\n    rv = self.dispatch_request()\n         ^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1469, in dispatch_request\n    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "E:\\device repair manaement system - Copy\\app\\routes\\auth.py", line 80, in register\n    if User.query.count() > 0:\n       ^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask_sqlalchemy\\model.py", line 30, in __get__\n    return cls.query_class(\n           ^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\sqlalchemy\\orm\\query.py", line 276, in __init__\n    self._set_entities(entities)\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\sqlalchemy\\orm\\query.py", line 288, in _set_entities\n    self._raw_columns = [\n                        ^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\sqlalchemy\\orm\\query.py", line 289, in <listcomp>\n    coercions.expect(\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\sqlalchemy\\sql\\coercions.py", line 406, in expect\n    insp._post_inspect\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\sqlalchemy\\util\\langhelpers.py", line 1260, in __get__\n    obj.__dict__[self.__name__] = result = self.fget(obj)\n                                           ^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\sqlalchemy\\orm\\mapper.py", line 2707, in _post_inspect\n    self._check_configure()\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\sqlalchemy\\orm\\mapper.py", line 2386, in _check_configure\n    _configure_registries({self.registry}, cascade=True)\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\sqlalchemy\\orm\\mapper.py", line 4199, in _configure_registries\n    _do_configure_registries(registries, cascade)\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\sqlalchemy\\orm\\mapper.py", line 4236, in _do_configure_registries\n    raise e\nsqlalchemy.exc.InvalidRequestError: One or more mappers failed to initialize - can\'t proceed with initialization of other mappers. Triggering mapper: \'Mapper[Customer(customer)]\'. Original exception was: Could not determine join condition between parent/child tables on relationship Customer.devices - there are no foreign keys linking these tables.  Ensure that referencing columns are associated with a ForeignKey or ForeignKeyConstraint, or specify a \'primaryjoin\' expression.\n'}
2025-06-01 13:52:38,074 - device_repair_system - INFO - Error handling system initialized
2025-06-01 13:52:39,268 - device_repair_system - INFO - Error handling system initialized
2025-06-01 13:52:39,358 - device_repair_system - INFO - Error handling system initialized
2025-06-01 13:52:39,456 - device_repair_system - INFO - Error handling system initialized
2025-06-01 13:52:39,549 - device_repair_system - INFO - Error handling system initialized
2025-06-01 13:54:16,972 - device_repair_system - INFO - Error handling system initialized
2025-06-01 13:54:18,005 - device_repair_system - INFO - Error handling system initialized
2025-06-01 13:54:18,086 - device_repair_system - INFO - Error handling system initialized
2025-06-01 13:59:48,506 - device_repair_system - INFO - Error handling system initialized
2025-06-01 14:00:14,759 - device_repair_system - ERROR - Server Error: {'timestamp': '2025-06-01T14:00:14.751066', 'status_code': 500, 'error_type': 'InvalidRequestError', 'error_message': "One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[User(user)]'. Original exception was: Could not determine join condition between parent/child tables on relationship User.devices - there are no foreign keys linking these tables.  Ensure that referencing columns are associated with a ForeignKey or ForeignKeyConstraint, or specify a 'primaryjoin' expression.", 'url': 'http://127.0.0.1:5000/auth/login?next=/dashboard/', 'method': 'POST', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous', 'traceback': 'Traceback (most recent call last):\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1484, in full_dispatch_request\n    rv = self.dispatch_request()\n         ^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1469, in dispatch_request\n    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "E:\\device repair manaement system - Copy\\app\\routes\\auth.py", line 27, in login\n    user = User.query.filter_by(username=username).first()\n           ^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask_sqlalchemy\\model.py", line 30, in __get__\n    return cls.query_class(\n           ^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\sqlalchemy\\orm\\query.py", line 276, in __init__\n    self._set_entities(entities)\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\sqlalchemy\\orm\\query.py", line 288, in _set_entities\n    self._raw_columns = [\n                        ^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\sqlalchemy\\orm\\query.py", line 289, in <listcomp>\n    coercions.expect(\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\sqlalchemy\\sql\\coercions.py", line 406, in expect\n    insp._post_inspect\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\sqlalchemy\\util\\langhelpers.py", line 1260, in __get__\n    obj.__dict__[self.__name__] = result = self.fget(obj)\n                                           ^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\sqlalchemy\\orm\\mapper.py", line 2707, in _post_inspect\n    self._check_configure()\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\sqlalchemy\\orm\\mapper.py", line 2386, in _check_configure\n    _configure_registries({self.registry}, cascade=True)\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\sqlalchemy\\orm\\mapper.py", line 4199, in _configure_registries\n    _do_configure_registries(registries, cascade)\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\sqlalchemy\\orm\\mapper.py", line 4236, in _do_configure_registries\n    raise e\nsqlalchemy.exc.InvalidRequestError: One or more mappers failed to initialize - can\'t proceed with initialization of other mappers. Triggering mapper: \'Mapper[User(user)]\'. Original exception was: Could not determine join condition between parent/child tables on relationship User.devices - there are no foreign keys linking these tables.  Ensure that referencing columns are associated with a ForeignKey or ForeignKeyConstraint, or specify a \'primaryjoin\' expression.\n'}
2025-06-01 14:00:26,095 - device_repair_system - ERROR - Server Error: {'timestamp': '2025-06-01T14:00:26.094447', 'status_code': 500, 'error_type': 'InvalidRequestError', 'error_message': "One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[User(user)]'. Original exception was: Could not determine join condition between parent/child tables on relationship User.devices - there are no foreign keys linking these tables.  Ensure that referencing columns are associated with a ForeignKey or ForeignKeyConstraint, or specify a 'primaryjoin' expression.", 'url': 'http://127.0.0.1:5000/auth/register', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous', 'traceback': 'Traceback (most recent call last):\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1484, in full_dispatch_request\n    rv = self.dispatch_request()\n         ^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1469, in dispatch_request\n    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "E:\\device repair manaement system - Copy\\app\\routes\\auth.py", line 80, in register\n    if User.query.count() > 0:\n       ^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask_sqlalchemy\\model.py", line 30, in __get__\n    return cls.query_class(\n           ^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\sqlalchemy\\orm\\query.py", line 276, in __init__\n    self._set_entities(entities)\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\sqlalchemy\\orm\\query.py", line 288, in _set_entities\n    self._raw_columns = [\n                        ^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\sqlalchemy\\orm\\query.py", line 289, in <listcomp>\n    coercions.expect(\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\sqlalchemy\\sql\\coercions.py", line 406, in expect\n    insp._post_inspect\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\sqlalchemy\\util\\langhelpers.py", line 1260, in __get__\n    obj.__dict__[self.__name__] = result = self.fget(obj)\n                                           ^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\sqlalchemy\\orm\\mapper.py", line 2707, in _post_inspect\n    self._check_configure()\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\sqlalchemy\\orm\\mapper.py", line 2386, in _check_configure\n    _configure_registries({self.registry}, cascade=True)\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\sqlalchemy\\orm\\mapper.py", line 4199, in _configure_registries\n    _do_configure_registries(registries, cascade)\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\sqlalchemy\\orm\\mapper.py", line 4236, in _do_configure_registries\n    raise e\nsqlalchemy.exc.InvalidRequestError: One or more mappers failed to initialize - can\'t proceed with initialization of other mappers. Triggering mapper: \'Mapper[User(user)]\'. Original exception was: Could not determine join condition between parent/child tables on relationship User.devices - there are no foreign keys linking these tables.  Ensure that referencing columns are associated with a ForeignKey or ForeignKeyConstraint, or specify a \'primaryjoin\' expression.\n'}
2025-06-01 14:02:04,557 - device_repair_system - INFO - Error handling system initialized
2025-06-01 14:02:05,804 - device_repair_system - INFO - Error handling system initialized
2025-06-01 14:02:06,742 - device_repair_system - INFO - Error handling system initialized
2025-06-01 14:02:40,128 - device_repair_system - INFO - Error handling system initialized
2025-06-01 14:02:41,383 - device_repair_system - INFO - Error handling system initialized
2025-06-01 14:02:42,185 - device_repair_system - INFO - Error handling system initialized
2025-06-01 14:03:33,568 - device_repair_system - INFO - Error handling system initialized
2025-06-01 14:11:22,995 - device_repair_system - INFO - Error handling system initialized
2025-06-01 14:11:28,385 - device_repair_system - WARNING - Client Error: {'timestamp': '2025-06-01T14:11:28.384060', 'status_code': 404, 'error_type': 'NotFound', 'error_message': '404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.', 'url': 'http://127.0.0.1:5000/favicon.ico', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous'}
2025-06-01 14:19:01,554 - device_repair_system - INFO - Error handling system initialized
2025-06-01 14:20:32,554 - device_repair_system - INFO - Error handling system initialized
2025-06-01 14:22:25,377 - device_repair_system - INFO - Error handling system initialized
2025-06-01 14:22:37,794 - device_repair_system - WARNING - Client Error: {'timestamp': '2025-06-01T14:22:37.793588', 'status_code': 404, 'error_type': 'NotFound', 'error_message': '404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.', 'url': 'http://127.0.0.1:5000/favicon.ico', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous'}
2025-06-01 14:29:10,878 - device_repair_system - INFO - Error handling system initialized
2025-06-01 14:29:16,323 - device_repair_system - WARNING - Client Error: {'timestamp': '2025-06-01T14:29:16.323963', 'status_code': 404, 'error_type': 'NotFound', 'error_message': '404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.', 'url': 'http://127.0.0.1:5000/favicon.ico', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous'}
2025-06-01 14:34:49,119 - device_repair_system - INFO - Error handling system initialized
2025-06-01 14:35:06,559 - device_repair_system - ERROR - Server Error: {'timestamp': '2025-06-01T14:35:06.316548', 'status_code': 500, 'error_type': 'OperationalError', 'error_message': '(sqlite3.OperationalError) database is locked\n[SQL: SELECT user.id AS user_id, user.tenant_id AS user_tenant_id, user.username AS user_username, user.email AS user_email, user.password_hash AS user_password_hash, user.full_name AS user_full_name, user.phone AS user_phone, user.role AS user_role, user.created_at AS user_created_at, user.last_login AS user_last_login, user.is_active AS user_is_active, user.address AS user_address, user.position AS user_position, user.department AS user_department, user.email_verified AS user_email_verified, user.email_verification_sent_at AS user_email_verification_sent_at, user.telegram_chat_id AS user_telegram_chat_id, user.notifications_enabled AS user_notifications_enabled, user.notify_new_device AS user_notify_new_device, user.notify_status_change AS user_notify_status_change, user.notify_repair_complete AS user_notify_repair_complete, user.language_preference AS user_language_preference, user.trial_start_date AS user_trial_start_date, user.trial_end_date AS user_trial_end_date, user.is_trial_account AS user_is_trial_account, user.trial_expired AS user_trial_expired, user.license_type AS user_license_type \nFROM user \nWHERE user.id = ?]\n[parameters: (17,)]\n(Background on this error at: https://sqlalche.me/e/20/e3q8)', 'url': 'http://127.0.0.1:5000/accounting/', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous', 'traceback': 'Traceback (most recent call last):\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\sqlalchemy\\engine\\base.py", line 1969, in _exec_single_context\n    self.dialect.do_execute(\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\sqlalchemy\\engine\\default.py", line 922, in do_execute\n    cursor.execute(statement, parameters)\nsqlite3.OperationalError: database is locked\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1484, in full_dispatch_request\n    rv = self.dispatch_request()\n         ^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1469, in dispatch_request\n    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask_login\\utils.py", line 284, in decorated_view\n    elif not current_user.is_authenticated:\n             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\werkzeug\\local.py", line 311, in __get__\n    obj = instance._get_current_object()\n          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\werkzeug\\local.py", line 515, in _get_current_object\n    return get_name(local())\n                    ^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask_login\\utils.py", line 25, in <lambda>\n    current_user = LocalProxy(lambda: _get_user())\n                                      ^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask_login\\utils.py", line 370, in _get_user\n    current_app.login_manager._load_user()\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask_login\\login_manager.py", line 364, in _load_user\n    user = self._user_callback(user_id)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "E:\\device repair manaement system - Copy\\app\\models\\user.py", line 200, in load_user\n    return User.query.get(int(user_id))\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "<string>", line 2, in get\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\sqlalchemy\\util\\deprecations.py", line 386, in warned\n    return fn(*args, **kwargs)  # type: ignore[no-any-return]\n           ^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\sqlalchemy\\orm\\query.py", line 1136, in get\n    return self._get_impl(ident, loading.load_on_pk_identity)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\sqlalchemy\\orm\\query.py", line 1145, in _get_impl\n    return self.session._get_impl(\n           ^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\sqlalchemy\\orm\\session.py", line 3833, in _get_impl\n    return db_load_fn(\n           ^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\sqlalchemy\\orm\\loading.py", line 690, in load_on_pk_identity\n    session.execute(\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\sqlalchemy\\orm\\session.py", line 2308, in execute\n    return self._execute_internal(\n           ^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\sqlalchemy\\orm\\session.py", line 2190, in _execute_internal\n    result: Result[Any] = compile_state_cls.orm_execute_statement(\n                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\sqlalchemy\\orm\\context.py", line 293, in orm_execute_statement\n    result = conn.execute(\n             ^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\sqlalchemy\\engine\\base.py", line 1416, in execute\n    return meth(\n           ^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\sqlalchemy\\sql\\elements.py", line 516, in _execute_on_connection\n    return connection._execute_clauseelement(\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\sqlalchemy\\engine\\base.py", line 1639, in _execute_clauseelement\n    ret = self._execute_context(\n          ^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\sqlalchemy\\engine\\base.py", line 1848, in _execute_context\n    return self._exec_single_context(\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\sqlalchemy\\engine\\base.py", line 1988, in _exec_single_context\n    self._handle_dbapi_exception(\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\sqlalchemy\\engine\\base.py", line 2343, in _handle_dbapi_exception\n    raise sqlalchemy_exception.with_traceback(exc_info[2]) from e\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\sqlalchemy\\engine\\base.py", line 1969, in _exec_single_context\n    self.dialect.do_execute(\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\sqlalchemy\\engine\\default.py", line 922, in do_execute\n    cursor.execute(statement, parameters)\nsqlalchemy.exc.OperationalError: (sqlite3.OperationalError) database is locked\n[SQL: SELECT user.id AS user_id, user.tenant_id AS user_tenant_id, user.username AS user_username, user.email AS user_email, user.password_hash AS user_password_hash, user.full_name AS user_full_name, user.phone AS user_phone, user.role AS user_role, user.created_at AS user_created_at, user.last_login AS user_last_login, user.is_active AS user_is_active, user.address AS user_address, user.position AS user_position, user.department AS user_department, user.email_verified AS user_email_verified, user.email_verification_sent_at AS user_email_verification_sent_at, user.telegram_chat_id AS user_telegram_chat_id, user.notifications_enabled AS user_notifications_enabled, user.notify_new_device AS user_notify_new_device, user.notify_status_change AS user_notify_status_change, user.notify_repair_complete AS user_notify_repair_complete, user.language_preference AS user_language_preference, user.trial_start_date AS user_trial_start_date, user.trial_end_date AS user_trial_end_date, user.is_trial_account AS user_is_trial_account, user.trial_expired AS user_trial_expired, user.license_type AS user_license_type \nFROM user \nWHERE user.id = ?]\n[parameters: (17,)]\n(Background on this error at: https://sqlalche.me/e/20/e3q8)\n'}
2025-06-01 14:35:08,746 - device_repair_system - ERROR - Server Error: {'timestamp': '2025-06-01T14:35:08.742052', 'status_code': 500, 'error_type': 'OperationalError', 'error_message': '(sqlite3.OperationalError) database is locked\n[SQL: SELECT user.id AS user_id, user.tenant_id AS user_tenant_id, user.username AS user_username, user.email AS user_email, user.password_hash AS user_password_hash, user.full_name AS user_full_name, user.phone AS user_phone, user.role AS user_role, user.created_at AS user_created_at, user.last_login AS user_last_login, user.is_active AS user_is_active, user.address AS user_address, user.position AS user_position, user.department AS user_department, user.email_verified AS user_email_verified, user.email_verification_sent_at AS user_email_verification_sent_at, user.telegram_chat_id AS user_telegram_chat_id, user.notifications_enabled AS user_notifications_enabled, user.notify_new_device AS user_notify_new_device, user.notify_status_change AS user_notify_status_change, user.notify_repair_complete AS user_notify_repair_complete, user.language_preference AS user_language_preference, user.trial_start_date AS user_trial_start_date, user.trial_end_date AS user_trial_end_date, user.is_trial_account AS user_is_trial_account, user.trial_expired AS user_trial_expired, user.license_type AS user_license_type \nFROM user \nWHERE user.id = ?]\n[parameters: (17,)]\n(Background on this error at: https://sqlalche.me/e/20/e3q8)', 'url': 'http://127.0.0.1:5000/accounting/', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous', 'traceback': 'Traceback (most recent call last):\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\sqlalchemy\\engine\\base.py", line 1969, in _exec_single_context\n    self.dialect.do_execute(\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\sqlalchemy\\engine\\default.py", line 922, in do_execute\n    cursor.execute(statement, parameters)\nsqlite3.OperationalError: database is locked\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1484, in full_dispatch_request\n    rv = self.dispatch_request()\n         ^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1469, in dispatch_request\n    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask_login\\utils.py", line 284, in decorated_view\n    elif not current_user.is_authenticated:\n             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\werkzeug\\local.py", line 311, in __get__\n    obj = instance._get_current_object()\n          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\werkzeug\\local.py", line 515, in _get_current_object\n    return get_name(local())\n                    ^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask_login\\utils.py", line 25, in <lambda>\n    current_user = LocalProxy(lambda: _get_user())\n                                      ^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask_login\\utils.py", line 370, in _get_user\n    current_app.login_manager._load_user()\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask_login\\login_manager.py", line 364, in _load_user\n    user = self._user_callback(user_id)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "E:\\device repair manaement system - Copy\\app\\models\\user.py", line 200, in load_user\n    return User.query.get(int(user_id))\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "<string>", line 2, in get\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\sqlalchemy\\util\\deprecations.py", line 386, in warned\n    return fn(*args, **kwargs)  # type: ignore[no-any-return]\n           ^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\sqlalchemy\\orm\\query.py", line 1136, in get\n    return self._get_impl(ident, loading.load_on_pk_identity)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\sqlalchemy\\orm\\query.py", line 1145, in _get_impl\n    return self.session._get_impl(\n           ^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\sqlalchemy\\orm\\session.py", line 3833, in _get_impl\n    return db_load_fn(\n           ^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\sqlalchemy\\orm\\loading.py", line 690, in load_on_pk_identity\n    session.execute(\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\sqlalchemy\\orm\\session.py", line 2308, in execute\n    return self._execute_internal(\n           ^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\sqlalchemy\\orm\\session.py", line 2190, in _execute_internal\n    result: Result[Any] = compile_state_cls.orm_execute_statement(\n                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\sqlalchemy\\orm\\context.py", line 293, in orm_execute_statement\n    result = conn.execute(\n             ^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\sqlalchemy\\engine\\base.py", line 1416, in execute\n    return meth(\n           ^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\sqlalchemy\\sql\\elements.py", line 516, in _execute_on_connection\n    return connection._execute_clauseelement(\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\sqlalchemy\\engine\\base.py", line 1639, in _execute_clauseelement\n    ret = self._execute_context(\n          ^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\sqlalchemy\\engine\\base.py", line 1848, in _execute_context\n    return self._exec_single_context(\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\sqlalchemy\\engine\\base.py", line 1988, in _exec_single_context\n    self._handle_dbapi_exception(\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\sqlalchemy\\engine\\base.py", line 2343, in _handle_dbapi_exception\n    raise sqlalchemy_exception.with_traceback(exc_info[2]) from e\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\sqlalchemy\\engine\\base.py", line 1969, in _exec_single_context\n    self.dialect.do_execute(\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\sqlalchemy\\engine\\default.py", line 922, in do_execute\n    cursor.execute(statement, parameters)\nsqlalchemy.exc.OperationalError: (sqlite3.OperationalError) database is locked\n[SQL: SELECT user.id AS user_id, user.tenant_id AS user_tenant_id, user.username AS user_username, user.email AS user_email, user.password_hash AS user_password_hash, user.full_name AS user_full_name, user.phone AS user_phone, user.role AS user_role, user.created_at AS user_created_at, user.last_login AS user_last_login, user.is_active AS user_is_active, user.address AS user_address, user.position AS user_position, user.department AS user_department, user.email_verified AS user_email_verified, user.email_verification_sent_at AS user_email_verification_sent_at, user.telegram_chat_id AS user_telegram_chat_id, user.notifications_enabled AS user_notifications_enabled, user.notify_new_device AS user_notify_new_device, user.notify_status_change AS user_notify_status_change, user.notify_repair_complete AS user_notify_repair_complete, user.language_preference AS user_language_preference, user.trial_start_date AS user_trial_start_date, user.trial_end_date AS user_trial_end_date, user.is_trial_account AS user_is_trial_account, user.trial_expired AS user_trial_expired, user.license_type AS user_license_type \nFROM user \nWHERE user.id = ?]\n[parameters: (17,)]\n(Background on this error at: https://sqlalche.me/e/20/e3q8)\n'}
2025-06-01 14:46:23,771 - device_repair_system - INFO - Error handling system initialized
2025-06-01 15:07:59,574 - device_repair_system - INFO - Error handling system initialized
2025-06-01 15:08:44,829 - device_repair_system - INFO - Error handling system initialized
2025-06-01 15:09:43,248 - device_repair_system - INFO - Error handling system initialized
2025-06-01 15:09:45,135 - device_repair_system - INFO - Error handling system initialized
2025-06-01 15:09:54,700 - device_repair_system - INFO - Error handling system initialized
2025-06-01 15:12:35,697 - device_repair_system - INFO - Error handling system initialized
2025-06-01 15:13:46,139 - device_repair_system - INFO - Error handling system initialized
2025-06-01 15:20:01,566 - device_repair_system - INFO - Error handling system initialized
2025-06-01 15:20:07,839 - device_repair_system - WARNING - Client Error: {'timestamp': '2025-06-01T15:20:07.838838', 'status_code': 404, 'error_type': 'NotFound', 'error_message': '404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.', 'url': 'http://127.0.0.1:5000/favicon.ico', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous'}
2025-06-01 15:27:28,011 - device_repair_system - INFO - Error handling system initialized
2025-06-01 15:27:29,384 - device_repair_system - INFO - Error handling system initialized
2025-06-01 15:27:29,544 - device_repair_system - INFO - Error handling system initialized
2025-06-01 15:37:11,541 - device_repair_system - INFO - Error handling system initialized
2025-06-01 15:37:28,675 - device_repair_system - INFO - Error handling system initialized
2025-06-01 15:37:29,922 - device_repair_system - INFO - Error handling system initialized
2025-06-01 15:37:30,081 - device_repair_system - INFO - Error handling system initialized
2025-06-01 15:44:17,418 - device_repair_system - INFO - Error handling system initialized
2025-06-01 15:44:42,265 - device_repair_system - INFO - Error handling system initialized
2025-06-01 15:44:43,499 - device_repair_system - INFO - Error handling system initialized
2025-06-01 15:44:43,633 - device_repair_system - INFO - Error handling system initialized
2025-06-01 15:54:00,795 - device_repair_system - INFO - Error handling system initialized
2025-06-01 16:00:48,102 - device_repair_system - INFO - Error handling system initialized
2025-06-01 16:01:03,962 - device_repair_system - WARNING - Client Error: {'timestamp': '2025-06-01T16:01:03.962250', 'status_code': 404, 'error_type': 'NotFound', 'error_message': '404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.', 'url': 'http://127.0.0.1:5000/favicon.ico', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous'}
2025-06-01 16:03:29,945 - device_repair_system - INFO - Error handling system initialized
2025-06-01 16:04:17,657 - device_repair_system - INFO - Error handling system initialized
2025-06-01 16:08:53,130 - device_repair_system - WARNING - Client Error: {'timestamp': '2025-06-01T16:08:53.129984', 'status_code': 404, 'error_type': 'NotFound', 'error_message': '404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.', 'url': 'http://localhost:5000/favicon.ico', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous'}
2025-06-01 16:09:33,543 - device_repair_system - INFO - Error handling system initialized
2025-06-01 16:11:53,852 - device_repair_system - ERROR - Server Error: {'timestamp': '2025-06-01T16:11:53.730739', 'status_code': 500, 'error_type': 'TemplateNotFound', 'error_message': 'employee/add.html', 'url': 'http://localhost:5000/employee/add', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous', 'traceback': 'Traceback (most recent call last):\n  File "E:\\device repair manaement system - Copy\\.venv\\Lib\\site-packages\\flask\\app.py", line 1484, in full_dispatch_request\n    rv = self.dispatch_request()\n         ^^^^^^^^^^^^^^^^^^^^^^^\n  File "E:\\device repair manaement system - Copy\\.venv\\Lib\\site-packages\\flask\\app.py", line 1469, in dispatch_request\n    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "E:\\device repair manaement system - Copy\\.venv\\Lib\\site-packages\\flask_login\\utils.py", line 290, in decorated_view\n    return current_app.ensure_sync(func)(*args, **kwargs)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "E:\\device repair manaement system - Copy\\app\\routes\\employee_management.py", line 99, in add_employee\n    return render_template(\'employee/add.html\')\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "E:\\device repair manaement system - Copy\\.venv\\Lib\\site-packages\\flask\\templating.py", line 150, in render_template\n    template = app.jinja_env.get_or_select_template(template_name_or_list)\n               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "E:\\device repair manaement system - Copy\\.venv\\Lib\\site-packages\\jinja2\\environment.py", line 1087, in get_or_select_template\n    return self.get_template(template_name_or_list, parent, globals)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "E:\\device repair manaement system - Copy\\.venv\\Lib\\site-packages\\jinja2\\environment.py", line 1016, in get_template\n    return self._load_template(name, globals)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "E:\\device repair manaement system - Copy\\.venv\\Lib\\site-packages\\jinja2\\environment.py", line 975, in _load_template\n    template = self.loader.load(self, name, self.make_globals(globals))\n               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "E:\\device repair manaement system - Copy\\.venv\\Lib\\site-packages\\jinja2\\loaders.py", line 126, in load\n    source, filename, uptodate = self.get_source(environment, name)\n                                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "E:\\device repair manaement system - Copy\\.venv\\Lib\\site-packages\\flask\\templating.py", line 64, in get_source\n    return self._get_source_fast(environment, template)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "E:\\device repair manaement system - Copy\\.venv\\Lib\\site-packages\\flask\\templating.py", line 98, in _get_source_fast\n    raise TemplateNotFound(template)\njinja2.exceptions.TemplateNotFound: employee/add.html\n'}
2025-06-01 16:13:46,614 - device_repair_system - INFO - Error handling system initialized
2025-06-01 16:14:02,511 - device_repair_system - INFO - Error handling system initialized
2025-06-01 16:15:57,926 - device_repair_system - INFO - Error handling system initialized
2025-06-01 16:18:26,745 - device_repair_system - INFO - Error handling system initialized
2025-06-01 16:19:01,041 - device_repair_system - INFO - Error handling system initialized
2025-06-01 16:22:59,343 - device_repair_system - INFO - Error handling system initialized
2025-06-01 16:23:51,049 - device_repair_system - INFO - Error handling system initialized
2025-06-01 16:24:42,753 - device_repair_system - INFO - Error handling system initialized
2025-06-01 16:27:50,542 - device_repair_system - INFO - Error handling system initialized
2025-06-01 16:28:12,288 - device_repair_system - WARNING - Client Error: {'timestamp': '2025-06-01T16:28:12.288020', 'status_code': 404, 'error_type': 'NotFound', 'error_message': '404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.', 'url': 'http://127.0.0.1:5000/favicon.ico', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous'}
2025-06-01 16:31:25,295 - device_repair_system - INFO - Error handling system initialized
2025-06-01 16:37:23,123 - device_repair_system - INFO - Error handling system initialized
2025-06-01 16:37:52,834 - device_repair_system - WARNING - Client Error: {'timestamp': '2025-06-01T16:37:52.833499', 'status_code': 404, 'error_type': 'NotFound', 'error_message': '404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.', 'url': 'http://localhost:5000/favicon.ico', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous'}
2025-06-01 16:38:27,104 - device_repair_system - INFO - Error handling system initialized
2025-06-01 16:40:02,645 - device_repair_system - INFO - Error handling system initialized
2025-06-01 16:44:48,254 - device_repair_system - INFO - Error handling system initialized
2025-06-01 16:47:38,620 - device_repair_system - INFO - Error handling system initialized
2025-06-01 16:48:50,680 - device_repair_system - INFO - Error handling system initialized
2025-06-01 16:48:58,229 - device_repair_system - WARNING - Client Error: {'timestamp': '2025-06-01T16:48:58.229495', 'status_code': 404, 'error_type': 'NotFound', 'error_message': '404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.', 'url': 'http://127.0.0.1:5000/favicon.ico', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous'}
2025-06-01 16:55:55,647 - device_repair_system - INFO - Error handling system initialized
2025-06-01 17:01:10,920 - device_repair_system - INFO - Error handling system initialized
2025-06-01 17:11:00,082 - device_repair_system - INFO - Error handling system initialized
2025-06-01 17:13:41,654 - device_repair_system - INFO - Error handling system initialized
2025-06-01 17:16:35,337 - device_repair_system - INFO - Error handling system initialized
2025-06-01 17:17:05,623 - device_repair_system - INFO - Error handling system initialized
2025-06-01 17:18:34,932 - device_repair_system - WARNING - Client Error: {'timestamp': '2025-06-01T17:18:34.931730', 'status_code': 404, 'error_type': 'NotFound', 'error_message': '404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.', 'url': 'http://127.0.0.1:5000/favicon.ico', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous'}
2025-06-01 17:23:26,699 - device_repair_system - INFO - Error handling system initialized
2025-06-01 17:23:49,116 - device_repair_system - INFO - Error handling system initialized
2025-06-01 17:23:50,378 - device_repair_system - INFO - Error handling system initialized
2025-06-01 17:24:28,507 - device_repair_system - INFO - Error handling system initialized
2025-06-01 17:28:57,730 - device_repair_system - INFO - Error handling system initialized
2025-06-01 17:35:01,876 - device_repair_system - INFO - Error handling system initialized
2025-06-01 17:49:06,411 - device_repair_system - INFO - Error handling system initialized
2025-06-01 17:59:45,913 - device_repair_system - INFO - Error handling system initialized
2025-06-01 17:59:48,306 - device_repair_system - INFO - Error handling system initialized
2025-06-01 18:03:06,683 - device_repair_system - INFO - Error handling system initialized
2025-06-01 18:03:11,334 - device_repair_system - WARNING - Client Error: {'timestamp': '2025-06-01T18:03:11.334485', 'status_code': 404, 'error_type': 'NotFound', 'error_message': '404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.', 'url': 'http://127.0.0.1:5000/favicon.ico', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous'}
2025-06-01 18:03:34,062 - device_repair_system - INFO - Error handling system initialized
2025-06-01 18:05:07,075 - device_repair_system - INFO - Error handling system initialized
2025-06-01 18:08:15,918 - device_repair_system - INFO - Error handling system initialized
2025-06-01 18:08:38,954 - device_repair_system - INFO - Error handling system initialized
2025-06-01 18:11:01,246 - device_repair_system - INFO - Error handling system initialized
2025-06-01 18:12:00,740 - device_repair_system - INFO - Error handling system initialized
2025-06-01 18:12:39,209 - device_repair_system - INFO - Error handling system initialized
2025-06-01 18:13:01,453 - device_repair_system - INFO - Error handling system initialized
2025-06-01 18:14:08,521 - device_repair_system - INFO - Error handling system initialized
2025-06-01 18:14:31,684 - device_repair_system - INFO - Error handling system initialized
2025-06-01 18:15:48,061 - device_repair_system - INFO - Error handling system initialized
2025-06-01 18:19:50,508 - device_repair_system - INFO - Error handling system initialized
2025-06-01 18:21:13,892 - device_repair_system - INFO - Error handling system initialized
2025-06-01 18:23:48,462 - device_repair_system - INFO - Error handling system initialized
2025-06-01 18:24:45,533 - device_repair_system - INFO - Error handling system initialized
2025-06-01 18:24:47,856 - device_repair_system - INFO - Error handling system initialized
2025-06-01 18:26:36,582 - device_repair_system - INFO - Error handling system initialized
2025-06-01 18:32:04,833 - device_repair_system - INFO - Error handling system initialized
2025-06-01 18:33:33,117 - device_repair_system - INFO - Error handling system initialized
2025-06-01 18:37:19,579 - device_repair_system - INFO - Error handling system initialized
2025-06-01 18:38:36,453 - device_repair_system - INFO - Error handling system initialized
2025-06-01 18:41:32,007 - device_repair_system - INFO - Error handling system initialized
2025-06-01 18:41:53,734 - device_repair_system - INFO - Error handling system initialized
2025-06-01 18:43:42,391 - device_repair_system - INFO - Error handling system initialized
2025-06-01 18:45:35,961 - device_repair_system - INFO - Error handling system initialized
2025-06-01 18:47:28,230 - device_repair_system - INFO - Error handling system initialized
2025-06-01 18:48:48,945 - device_repair_system - INFO - Error handling system initialized
2025-06-01 18:52:28,413 - device_repair_system - INFO - Error handling system initialized
2025-06-01 18:57:45,127 - device_repair_system - INFO - Error handling system initialized
2025-06-01 19:06:18,783 - device_repair_system - INFO - Error handling system initialized
2025-06-01 19:06:23,659 - device_repair_system - WARNING - Client Error: {'timestamp': '2025-06-01T19:06:23.658429', 'status_code': 404, 'error_type': 'NotFound', 'error_message': '404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.', 'url': 'http://127.0.0.1:5000/favicon.ico', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous'}
2025-06-01 19:08:35,487 - device_repair_system - INFO - Error handling system initialized
2025-06-01 19:09:05,212 - device_repair_system - INFO - Error handling system initialized
2025-06-01 19:09:07,480 - device_repair_system - INFO - Error handling system initialized
2025-06-01 19:09:26,119 - device_repair_system - INFO - Error handling system initialized
2025-06-01 19:09:45,870 - device_repair_system - INFO - Error handling system initialized
2025-06-01 19:10:05,564 - device_repair_system - INFO - Error handling system initialized
2025-06-01 19:16:23,226 - device_repair_system - INFO - Error handling system initialized
2025-06-01 19:23:04,915 - device_repair_system - INFO - Error handling system initialized
2025-06-01 19:33:40,451 - device_repair_system - INFO - Error handling system initialized
2025-06-01 19:37:10,615 - device_repair_system - INFO - Error handling system initialized
2025-06-01 19:45:06,840 - device_repair_system - INFO - Error handling system initialized
2025-06-01 20:00:00,069 - device_repair_system - INFO - Error handling system initialized
2025-06-01 20:02:59,361 - device_repair_system - INFO - Error handling system initialized
2025-06-01 20:15:51,719 - device_repair_system - INFO - Error handling system initialized
2025-06-01 20:21:26,543 - device_repair_system - INFO - Error handling system initialized
2025-06-01 20:37:16,946 - device_repair_system - INFO - Error handling system initialized
2025-06-01 21:08:31,740 - device_repair_system - INFO - Error handling system initialized
2025-06-01 21:39:07,509 - device_repair_system - INFO - Error handling system initialized
2025-06-01 21:42:38,070 - device_repair_system - INFO - Error handling system initialized
2025-06-01 22:27:08,058 - device_repair_system - INFO - Error handling system initialized
2025-06-01 22:29:10,447 - device_repair_system - INFO - Error handling system initialized
2025-06-01 22:30:29,232 - device_repair_system - INFO - Error handling system initialized
2025-06-01 22:35:00,605 - device_repair_system - INFO - Error handling system initialized
2025-06-01 23:22:25,993 - device_repair_system - INFO - Error handling system initialized
2025-06-01 23:28:51,291 - device_repair_system - INFO - Error handling system initialized
2025-06-02 00:26:05,211 - device_repair_system - INFO - Error handling system initialized
2025-06-02 00:27:50,731 - device_repair_system - INFO - Error handling system initialized
2025-06-02 00:27:56,461 - device_repair_system - WARNING - Client Error: {'timestamp': '2025-06-02T00:27:56.461852', 'status_code': 404, 'error_type': 'NotFound', 'error_message': '404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.', 'url': 'http://127.0.0.1:5000/favicon.ico', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous'}
2025-06-02 00:38:07,841 - device_repair_system - INFO - Error handling system initialized
2025-06-02 14:30:48,637 - device_repair_system - INFO - Error handling system initialized
2025-06-02 14:39:30,427 - device_repair_system - INFO - Error handling system initialized
2025-06-02 14:40:14,042 - device_repair_system - WARNING - Client Error: {'timestamp': '2025-06-02T14:40:14.042814', 'status_code': 404, 'error_type': 'NotFound', 'error_message': '404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.', 'url': 'http://localhost:5000/favicon.ico', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous'}
2025-06-02 15:11:56,121 - device_repair_system - INFO - Error handling system initialized
2025-06-02 15:12:33,615 - device_repair_system - WARNING - Client Error: {'timestamp': '2025-06-02T15:12:33.615591', 'status_code': 404, 'error_type': 'NotFound', 'error_message': '404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.', 'url': 'http://127.0.0.1:5000/favicon.ico', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous'}
2025-06-02 15:18:36,489 - device_repair_system - INFO - Error handling system initialized
2025-06-02 15:18:41,192 - device_repair_system - INFO - Error handling system initialized
2025-06-02 15:22:49,703 - device_repair_system - INFO - Error handling system initialized
2025-06-02 15:23:18,038 - device_repair_system - INFO - Error handling system initialized
2025-06-02 15:23:18,611 - device_repair_system - INFO - Error handling system initialized
2025-06-02 15:23:21,466 - device_repair_system - INFO - Error handling system initialized
2025-06-02 15:26:07,077 - device_repair_system - INFO - Error handling system initialized
2025-06-02 15:26:49,120 - device_repair_system - INFO - Error handling system initialized
2025-06-02 15:30:55,689 - device_repair_system - WARNING - Client Error: {'timestamp': '2025-06-02T15:30:55.689366', 'status_code': 404, 'error_type': 'NotFound', 'error_message': '404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.', 'url': 'http://127.0.0.1:5000/devices/85/create_ticket', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous'}
2025-06-02 15:33:28,472 - device_repair_system - INFO - Error handling system initialized
2025-06-02 15:33:37,141 - device_repair_system - WARNING - Client Error: {'timestamp': '2025-06-02T15:33:37.141298', 'status_code': 404, 'error_type': 'NotFound', 'error_message': '404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.', 'url': 'http://127.0.0.1:5000/favicon.ico', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous'}
2025-06-02 15:47:07,582 - device_repair_system - INFO - Error handling system initialized
2025-06-02 15:51:09,268 - device_repair_system - INFO - Error handling system initialized
2025-06-02 15:56:18,126 - device_repair_system - INFO - Error handling system initialized
2025-06-02 16:02:21,874 - device_repair_system - INFO - Error handling system initialized
2025-06-02 16:02:24,599 - device_repair_system - INFO - Error handling system initialized
2025-06-02 16:10:24,830 - device_repair_system - INFO - Error handling system initialized
2025-06-02 16:17:11,203 - device_repair_system - INFO - Error handling system initialized
2025-06-02 16:17:12,636 - device_repair_system - INFO - Error handling system initialized
2025-06-02 16:19:24,258 - device_repair_system - INFO - Error handling system initialized
2025-06-02 16:19:25,649 - device_repair_system - INFO - Error handling system initialized
2025-06-02 16:20:33,672 - device_repair_system - INFO - Error handling system initialized
2025-06-02 16:20:42,821 - device_repair_system - INFO - Error handling system initialized
2025-06-02 16:21:01,243 - device_repair_system - INFO - Error handling system initialized
2025-06-02 16:21:03,879 - device_repair_system - INFO - Error handling system initialized
2025-06-02 16:55:21,611 - device_repair_system - INFO - Error handling system initialized
2025-06-02 16:57:03,847 - device_repair_system - INFO - Error handling system initialized
2025-06-02 16:59:19,369 - device_repair_system - INFO - Error handling system initialized
2025-06-02 16:59:20,575 - device_repair_system - INFO - Error handling system initialized
2025-06-02 16:59:22,648 - device_repair_system - INFO - Error handling system initialized
2025-06-02 16:59:24,144 - device_repair_system - INFO - Error handling system initialized
2025-06-02 17:15:19,185 - device_repair_system - INFO - Error handling system initialized
2025-06-02 17:16:14,075 - device_repair_system - INFO - Error handling system initialized
2025-06-02 17:16:17,220 - device_repair_system - INFO - Error handling system initialized
2025-06-02 17:16:40,066 - device_repair_system - ERROR - Server Error: {'timestamp': '2025-06-02T17:16:39.971989', 'status_code': 500, 'error_type': 'BuildError', 'error_message': "Could not build url for endpoint 'simple_inventory.index'. Did you mean 'enhanced_simple_inventory.index' instead?", 'url': 'http://127.0.0.1:5000/dashboard/', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous', 'traceback': 'Traceback (most recent call last):\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1484, in full_dispatch_request\n    rv = self.dispatch_request()\n         ^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1469, in dispatch_request\n    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask_login\\utils.py", line 290, in decorated_view\n    return current_app.ensure_sync(func)(*args, **kwargs)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "E:\\device repair manaement system - Copy\\app\\routes\\dashboard.py", line 47, in index\n    return render_template(\n           ^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\templating.py", line 151, in render_template\n    return _render(app, template, context)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\templating.py", line 132, in _render\n    rv = template.render(context)\n         ^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\environment.py", line 1295, in render\n    self.environment.handle_exception()\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\environment.py", line 942, in handle_exception\n    raise rewrite_traceback_stack(source=source)\n  File "E:\\device repair manaement system - Copy\\app\\templates\\dashboard\\index.html", line 1, in top-level template code\n    {% extends \'base.html\' %}\n  File "E:\\device repair manaement system - Copy\\app\\templates\\base.html", line 392, in top-level template code\n    href="{{ url_for(\'simple_inventory.index\') }}">\n    ^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1697, in url_for\n    return self.handle_url_build_error(error, endpoint, values)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1686, in url_for\n    rv = url_adapter.build(  # type: ignore[union-attr]\n         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\werkzeug\\routing\\map.py", line 950, in build\n    raise BuildError(endpoint, values, method, self)\nwerkzeug.routing.exceptions.BuildError: Could not build url for endpoint \'simple_inventory.index\'. Did you mean \'enhanced_simple_inventory.index\' instead?\n'}
2025-06-02 17:21:21,810 - device_repair_system - INFO - Error handling system initialized
2025-06-02 17:21:24,704 - device_repair_system - INFO - Error handling system initialized
2025-06-02 17:32:24,948 - device_repair_system - INFO - Error handling system initialized
2025-06-02 17:32:27,707 - device_repair_system - INFO - Error handling system initialized
2025-06-02 17:40:47,783 - device_repair_system - INFO - Error handling system initialized
2025-06-02 17:41:01,281 - device_repair_system - WARNING - Client Error: {'timestamp': '2025-06-02T17:41:01.280199', 'status_code': 404, 'error_type': 'NotFound', 'error_message': '404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.', 'url': 'http://127.0.0.1:5000/favicon.ico', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous'}
2025-06-02 17:41:07,059 - device_repair_system - WARNING - Client Error: {'timestamp': '2025-06-02T17:41:07.059018', 'status_code': 404, 'error_type': 'NotFound', 'error_message': '404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.', 'url': 'http://127.0.0.1:5000/enhanced_simple_inventory', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous'}
2025-06-02 17:43:35,429 - device_repair_system - INFO - Error handling system initialized
2025-06-02 17:44:57,302 - device_repair_system - WARNING - Client Error: {'timestamp': '2025-06-02T17:44:57.302927', 'status_code': 404, 'error_type': 'NotFound', 'error_message': '404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.', 'url': 'http://127.0.0.1:5000/enhanced_simple_inventory/', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous'}
2025-06-02 17:45:11,837 - device_repair_system - INFO - Error handling system initialized
2025-06-02 17:54:31,327 - device_repair_system - INFO - Error handling system initialized
2025-06-02 17:54:48,176 - device_repair_system - WARNING - Client Error: {'timestamp': '2025-06-02T17:54:48.176962', 'status_code': 404, 'error_type': 'NotFound', 'error_message': '404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.', 'url': 'http://127.0.0.1:5000/enhanced_simple_inventory/', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous'}
2025-06-02 18:02:45,679 - device_repair_system - WARNING - Client Error: {'timestamp': '2025-06-02T18:02:45.679745', 'status_code': 404, 'error_type': 'NotFound', 'error_message': '404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.', 'url': 'http://127.0.0.1:5000/enhanced_simple_inventory/', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous'}
2025-06-02 18:05:49,009 - device_repair_system - INFO - Error handling system initialized
2025-06-02 18:07:34,543 - device_repair_system - INFO - Error handling system initialized
2025-06-02 18:13:05,054 - device_repair_system - INFO - Error handling system initialized
2025-06-02 18:15:41,639 - device_repair_system - INFO - Error handling system initialized
2025-06-02 18:16:01,129 - device_repair_system - WARNING - Client Error: {'timestamp': '2025-06-02T18:16:01.129400', 'status_code': 404, 'error_type': 'NotFound', 'error_message': '404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.', 'url': 'http://127.0.0.1:5000/favicon.ico', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous'}
2025-06-02 18:27:53,945 - device_repair_system - INFO - Error handling system initialized
2025-06-02 18:29:24,890 - device_repair_system - INFO - Error handling system initialized
2025-06-02 18:30:19,943 - device_repair_system - INFO - Error handling system initialized
2025-06-02 18:32:46,288 - device_repair_system - INFO - Error handling system initialized
2025-06-02 18:32:54,700 - device_repair_system - WARNING - Client Error: {'timestamp': '2025-06-02T18:32:54.700279', 'status_code': 404, 'error_type': 'NotFound', 'error_message': '404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.', 'url': 'http://127.0.0.1:5000/favicon.ico', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous'}
2025-06-02 19:05:03,195 - device_repair_system - INFO - Error handling system initialized
2025-06-02 19:05:04,448 - device_repair_system - INFO - Error handling system initialized
2025-06-02 19:16:31,162 - device_repair_system - INFO - Error handling system initialized
2025-06-02 19:18:22,929 - device_repair_system - INFO - Error handling system initialized
2025-06-02 19:19:03,379 - device_repair_system - INFO - Error handling system initialized
2025-06-02 19:19:07,787 - device_repair_system - WARNING - Client Error: {'timestamp': '2025-06-02T19:19:07.787410', 'status_code': 404, 'error_type': 'NotFound', 'error_message': '404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.', 'url': 'http://127.0.0.1:5000/favicon.ico', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous'}
2025-06-02 19:21:45,489 - device_repair_system - INFO - Error handling system initialized
2025-06-02 19:22:55,772 - device_repair_system - INFO - Error handling system initialized
2025-06-02 19:31:37,070 - device_repair_system - INFO - Error handling system initialized
2025-06-02 19:50:31,025 - device_repair_system - INFO - Error handling system initialized
2025-06-02 19:50:36,219 - device_repair_system - WARNING - Client Error: {'timestamp': '2025-06-02T19:50:36.219900', 'status_code': 404, 'error_type': 'NotFound', 'error_message': '404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.', 'url': 'http://127.0.0.1:5000/favicon.ico', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous'}
2025-06-02 19:52:53,820 - device_repair_system - INFO - Error handling system initialized
2025-06-02 19:59:59,524 - device_repair_system - INFO - Error handling system initialized
2025-06-02 20:00:12,899 - device_repair_system - WARNING - Client Error: {'timestamp': '2025-06-02T20:00:12.899065', 'status_code': 404, 'error_type': 'NotFound', 'error_message': '404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.', 'url': 'http://127.0.0.1:5000/favicon.ico', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous'}
2025-06-02 20:09:52,696 - device_repair_system - INFO - Error handling system initialized
2025-06-02 20:10:12,342 - device_repair_system - WARNING - Client Error: {'timestamp': '2025-06-02T20:10:12.341368', 'status_code': 404, 'error_type': 'NotFound', 'error_message': '404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.', 'url': 'http://127.0.0.1:5000/favicon.ico', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous'}
2025-06-02 20:16:04,179 - device_repair_system - INFO - Error handling system initialized
2025-06-02 20:16:13,618 - device_repair_system - WARNING - Client Error: {'timestamp': '2025-06-02T20:16:13.618440', 'status_code': 404, 'error_type': 'NotFound', 'error_message': '404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.', 'url': 'http://127.0.0.1:5000/favicon.ico', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous'}
2025-06-02 20:22:51,030 - device_repair_system - INFO - Error handling system initialized
2025-06-02 20:22:56,790 - device_repair_system - WARNING - Client Error: {'timestamp': '2025-06-02T20:22:56.790135', 'status_code': 404, 'error_type': 'NotFound', 'error_message': '404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.', 'url': 'http://127.0.0.1:5000/favicon.ico', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous'}
2025-06-02 20:23:31,193 - device_repair_system - INFO - Error handling system initialized
2025-06-02 20:23:35,739 - device_repair_system - WARNING - Client Error: {'timestamp': '2025-06-02T20:23:35.739942', 'status_code': 404, 'error_type': 'NotFound', 'error_message': '404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.', 'url': 'http://127.0.0.1:5000/favicon.ico', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous'}
2025-06-02 20:27:43,227 - device_repair_system - INFO - Error handling system initialized
2025-06-02 20:28:00,789 - device_repair_system - WARNING - Client Error: {'timestamp': '2025-06-02T20:28:00.788869', 'status_code': 404, 'error_type': 'NotFound', 'error_message': '404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.', 'url': 'http://127.0.0.1:5000/favicon.ico', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous'}
2025-06-02 20:35:53,286 - device_repair_system - INFO - Error handling system initialized
2025-06-02 20:43:03,239 - device_repair_system - INFO - Error handling system initialized
2025-06-02 20:45:01,034 - device_repair_system - INFO - Error handling system initialized
2025-06-02 20:48:06,956 - device_repair_system - INFO - Error handling system initialized
2025-06-02 20:49:18,215 - device_repair_system - INFO - Error handling system initialized
2025-06-02 20:49:39,301 - device_repair_system - WARNING - Client Error: {'timestamp': '2025-06-02T20:49:39.301885', 'status_code': 404, 'error_type': 'NotFound', 'error_message': '404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.', 'url': 'http://127.0.0.1:5000/favicon.ico', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous'}
2025-06-02 20:55:18,860 - device_repair_system - INFO - Error handling system initialized
2025-06-02 21:04:34,693 - device_repair_system - INFO - Error handling system initialized
2025-06-02 21:04:47,272 - device_repair_system - INFO - Error handling system initialized
2025-06-02 21:04:52,258 - device_repair_system - WARNING - Client Error: {'timestamp': '2025-06-02T21:04:52.257423', 'status_code': 404, 'error_type': 'NotFound', 'error_message': '404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.', 'url': 'http://127.0.0.1:5000/favicon.ico', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous'}
2025-06-02 21:05:52,241 - device_repair_system - WARNING - Client Error: {'timestamp': '2025-06-02T21:05:52.240767', 'status_code': 404, 'error_type': 'NotFound', 'error_message': '404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.', 'url': 'http://127.0.0.1:5000/favicon.ico', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous'}
2025-06-02 21:06:44,206 - device_repair_system - WARNING - Client Error: {'timestamp': '2025-06-02T21:06:44.206832', 'status_code': 404, 'error_type': 'NotFound', 'error_message': '404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.', 'url': 'http://127.0.0.1:5000/favicon.ico', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous'}
2025-06-02 21:09:41,486 - device_repair_system - WARNING - Client Error: {'timestamp': '2025-06-02T21:09:41.485313', 'status_code': 400, 'error_type': 'CSRFError', 'error_message': '400 Bad Request: The CSRF token is missing.', 'url': 'http://127.0.0.1:5000/api/activation-sync/sync-new-code', 'method': 'POST', 'user_agent': 'python-requests/2.31.0', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous'}
2025-06-02 21:10:59,586 - device_repair_system - INFO - Error handling system initialized
2025-06-02 21:11:41,725 - device_repair_system - INFO - Error handling system initialized
2025-06-02 21:11:56,166 - device_repair_system - INFO - Error handling system initialized
2025-06-02 21:13:37,188 - device_repair_system - WARNING - Client Error: {'timestamp': '2025-06-02T21:13:37.188292', 'status_code': 404, 'error_type': 'NotFound', 'error_message': '404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.', 'url': 'http://127.0.0.1:5000/favicon.ico', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous'}
2025-06-02 21:14:30,523 - device_repair_system - INFO - Error handling system initialized
2025-06-02 21:14:34,958 - device_repair_system - WARNING - Client Error: {'timestamp': '2025-06-02T21:14:34.957403', 'status_code': 404, 'error_type': 'NotFound', 'error_message': '404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.', 'url': 'http://127.0.0.1:5000/favicon.ico', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous'}
2025-06-02 21:47:33,180 - device_repair_system - INFO - Error handling system initialized
2025-06-02 21:49:57,322 - device_repair_system - INFO - Error handling system initialized
2025-06-02 21:50:28,896 - device_repair_system - WARNING - Client Error: {'timestamp': '2025-06-02T21:50:28.896517', 'status_code': 404, 'error_type': 'NotFound', 'error_message': '404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.', 'url': 'http://127.0.0.1:5000/favicon.ico', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous'}
2025-06-02 22:00:57,078 - device_repair_system - INFO - Error handling system initialized
2025-06-02 22:01:44,735 - device_repair_system - INFO - Error handling system initialized
2025-06-02 22:01:52,854 - device_repair_system - WARNING - Client Error: {'timestamp': '2025-06-02T22:01:52.853639', 'status_code': 404, 'error_type': 'NotFound', 'error_message': '404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.', 'url': 'http://127.0.0.1:5000/favicon.ico', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous'}
2025-06-02 22:01:54,867 - device_repair_system - WARNING - Client Error: {'timestamp': '2025-06-02T22:01:54.867525', 'status_code': 404, 'error_type': 'NotFound', 'error_message': '404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.', 'url': 'http://127.0.0.1:5000/favicon.ico', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous'}
2025-06-02 22:03:23,146 - device_repair_system - INFO - Error handling system initialized
2025-06-02 22:03:48,802 - device_repair_system - INFO - Error handling system initialized
2025-06-02 22:05:06,027 - device_repair_system - INFO - Error handling system initialized
2025-06-02 22:20:51,804 - device_repair_system - INFO - Error handling system initialized
2025-06-02 22:35:03,040 - device_repair_system - INFO - Error handling system initialized
2025-06-02 22:36:48,691 - device_repair_system - INFO - Error handling system initialized
2025-06-02 22:37:44,377 - device_repair_system - INFO - Error handling system initialized
2025-06-02 22:37:52,784 - device_repair_system - WARNING - Client Error: {'timestamp': '2025-06-02T22:37:52.783920', 'status_code': 404, 'error_type': 'NotFound', 'error_message': '404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.', 'url': 'http://127.0.0.1:5000/favicon.ico', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous'}
2025-06-02 22:41:35,677 - device_repair_system - WARNING - Client Error: {'timestamp': '2025-06-02T22:41:35.677406', 'status_code': 404, 'error_type': 'NotFound', 'error_message': '404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.', 'url': 'http://127.0.0.1:5000/.well-known/appspecific/com.chrome.devtools.json', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous'}
2025-06-02 22:46:29,983 - device_repair_system - INFO - Error handling system initialized
2025-06-02 22:49:04,334 - device_repair_system - INFO - Error handling system initialized
2025-06-02 22:49:27,922 - device_repair_system - INFO - Error handling system initialized
2025-06-02 22:49:36,549 - device_repair_system - WARNING - Client Error: {'timestamp': '2025-06-02T22:49:36.549690', 'status_code': 404, 'error_type': 'NotFound', 'error_message': '404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.', 'url': 'http://127.0.0.1:5000/favicon.ico', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous'}
2025-06-02 22:55:09,816 - device_repair_system - INFO - Error handling system initialized
2025-06-02 22:56:55,808 - device_repair_system - INFO - Error handling system initialized
2025-06-02 23:16:26,162 - device_repair_system - INFO - Error handling system initialized
2025-06-02 23:16:43,455 - device_repair_system - INFO - Error handling system initialized
2025-06-02 23:16:49,972 - device_repair_system - WARNING - Client Error: {'timestamp': '2025-06-02T23:16:49.972796', 'status_code': 404, 'error_type': 'NotFound', 'error_message': '404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.', 'url': 'http://127.0.0.1:5000/favicon.ico', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous'}
2025-06-02 23:18:26,251 - device_repair_system - INFO - Error handling system initialized
2025-06-02 23:19:45,799 - device_repair_system - INFO - Error handling system initialized
2025-06-02 23:19:46,973 - device_repair_system - INFO - Error handling system initialized
2025-06-02 23:19:47,721 - device_repair_system - INFO - Error handling system initialized
2025-06-02 23:19:47,922 - device_repair_system - INFO - Error handling system initialized
2025-06-02 23:21:51,570 - device_repair_system - INFO - Error handling system initialized
2025-06-02 23:23:28,211 - device_repair_system - WARNING - Client Error: {'timestamp': '2025-06-02T23:23:28.211534', 'status_code': 404, 'error_type': 'NotFound', 'error_message': '404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.', 'url': 'http://127.0.0.1:5000/favicon.ico', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous'}
2025-06-02 23:28:41,081 - device_repair_system - INFO - Error handling system initialized
2025-06-02 23:28:54,563 - device_repair_system - WARNING - Client Error: {'timestamp': '2025-06-02T23:28:54.563549', 'status_code': 404, 'error_type': 'NotFound', 'error_message': '404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.', 'url': 'http://127.0.0.1:5000/favicon.ico', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous'}
2025-06-02 23:30:09,092 - device_repair_system - WARNING - Client Error: {'timestamp': '2025-06-02T23:30:09.091161', 'status_code': 400, 'error_type': 'CSRFError', 'error_message': '400 Bad Request: The CSRF token is missing.', 'url': 'http://127.0.0.1:5000/auth/login', 'method': 'POST', 'user_agent': 'python-requests/2.31.0', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous'}
2025-06-02 23:30:34,674 - device_repair_system - WARNING - Client Error: {'timestamp': '2025-06-02T23:30:34.674972', 'status_code': 400, 'error_type': 'CSRFError', 'error_message': '400 Bad Request: The CSRF token is missing.', 'url': 'http://127.0.0.1:5000/auth/login', 'method': 'POST', 'user_agent': 'python-requests/2.31.0', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous'}
2025-06-02 23:31:24,683 - device_repair_system - INFO - Error handling system initialized
2025-06-02 23:31:32,875 - device_repair_system - WARNING - Client Error: {'timestamp': '2025-06-02T23:31:32.874072', 'status_code': 404, 'error_type': 'NotFound', 'error_message': '404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.', 'url': 'http://127.0.0.1:5000/favicon.ico', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous'}
2025-06-02 23:40:02,797 - device_repair_system - INFO - Error handling system initialized
2025-06-02 23:40:07,566 - device_repair_system - WARNING - Client Error: {'timestamp': '2025-06-02T23:40:07.566668', 'status_code': 404, 'error_type': 'NotFound', 'error_message': '404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.', 'url': 'http://127.0.0.1:5000/favicon.ico', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous'}
2025-06-03 12:26:26,382 - device_repair_system - INFO - Error handling system initialized
2025-06-03 12:26:45,064 - device_repair_system - WARNING - Client Error: {'timestamp': '2025-06-03T12:26:45.063727', 'status_code': 404, 'error_type': 'NotFound', 'error_message': '404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.', 'url': 'http://127.0.0.1:5000/favicon.ico', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous'}
2025-06-03 12:28:17,770 - device_repair_system - INFO - Error handling system initialized
2025-06-03 12:28:58,833 - device_repair_system - INFO - Error handling system initialized
2025-06-03 12:29:13,359 - device_repair_system - INFO - Error handling system initialized
2025-06-03 12:32:05,506 - device_repair_system - INFO - Error handling system initialized
2025-06-03 12:32:28,054 - device_repair_system - WARNING - Client Error: {'timestamp': '2025-06-03T12:32:28.053692', 'status_code': 404, 'error_type': 'NotFound', 'error_message': '404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.', 'url': 'http://127.0.0.1:5000/favicon.ico', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous'}
2025-06-03 13:19:23,254 - device_repair_system - INFO - Error handling system initialized
2025-06-03 13:20:14,487 - device_repair_system - WARNING - Client Error: {'timestamp': '2025-06-03T13:20:14.487342', 'status_code': 404, 'error_type': 'NotFound', 'error_message': '404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.', 'url': 'http://127.0.0.1:5000/favicon.ico', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous'}
2025-06-03 13:29:55,728 - device_repair_system - INFO - Error handling system initialized
2025-06-03 13:30:08,559 - device_repair_system - WARNING - Client Error: {'timestamp': '2025-06-03T13:30:08.558045', 'status_code': 404, 'error_type': 'NotFound', 'error_message': '404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.', 'url': 'http://127.0.0.1:5000/favicon.ico', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous'}
2025-06-03 13:41:47,443 - device_repair_system - INFO - Error handling system initialized
2025-06-03 13:48:25,776 - device_repair_system - INFO - Error handling system initialized
2025-06-03 13:48:30,712 - device_repair_system - WARNING - Client Error: {'timestamp': '2025-06-03T13:48:30.712987', 'status_code': 404, 'error_type': 'NotFound', 'error_message': '404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.', 'url': 'http://127.0.0.1:5000/favicon.ico', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous'}
2025-06-03 13:49:27,336 - device_repair_system - INFO - Error handling system initialized
2025-06-03 13:49:36,681 - device_repair_system - WARNING - Client Error: {'timestamp': '2025-06-03T13:49:36.681370', 'status_code': 404, 'error_type': 'NotFound', 'error_message': '404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.', 'url': 'http://127.0.0.1:5000/favicon.ico', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous'}
2025-06-03 13:52:06,339 - device_repair_system - INFO - Error handling system initialized
2025-06-03 13:54:55,630 - device_repair_system - INFO - Error handling system initialized
2025-06-03 13:55:13,778 - device_repair_system - WARNING - Client Error: {'timestamp': '2025-06-03T13:55:13.778852', 'status_code': 404, 'error_type': 'NotFound', 'error_message': '404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.', 'url': 'http://127.0.0.1:5000/favicon.ico', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous'}
2025-06-03 10:58:44,686 - device_repair_system - INFO - Error handling system initialized
2025-06-03 10:59:14,911 - device_repair_system - INFO - Error handling system initialized
2025-06-03 11:11:28,008 - device_repair_system - WARNING - Client Error: {'timestamp': '2025-06-03T11:11:28.008893', 'status_code': 404, 'error_type': 'NotFound', 'error_message': '404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.', 'url': 'http://127.0.0.1:5000/favicon.ico', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous'}
2025-06-03 11:28:28,547 - device_repair_system - INFO - Error handling system initialized
2025-06-03 11:59:52,240 - device_repair_system - INFO - Error handling system initialized
2025-06-03 12:10:51,436 - device_repair_system - INFO - Error handling system initialized
2025-06-03 12:11:08,791 - device_repair_system - WARNING - Client Error: {'timestamp': '2025-06-03T12:11:08.791097', 'status_code': 404, 'error_type': 'NotFound', 'error_message': '404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.', 'url': 'http://127.0.0.1:5000/favicon.ico', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous'}
2025-06-04 03:56:25,042 - device_repair_system - INFO - Error handling system initialized
2025-06-04 03:57:22,027 - device_repair_system - INFO - Error handling system initialized
2025-06-04 04:19:01,350 - device_repair_system - INFO - Error handling system initialized
2025-06-04 04:40:22,388 - device_repair_system - INFO - Error handling system initialized
2025-06-04 04:40:47,555 - device_repair_system - WARNING - Client Error: {'timestamp': '2025-06-04T04:40:47.554221', 'status_code': 404, 'error_type': 'NotFound', 'error_message': '404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.', 'url': 'http://127.0.0.1:5000/favicon.ico', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous'}
2025-06-04 04:40:50,689 - device_repair_system - WARNING - Client Error: {'timestamp': '2025-06-04T04:40:50.688614', 'status_code': 404, 'error_type': 'NotFound', 'error_message': '404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.', 'url': 'http://127.0.0.1:5000/tenant/register', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous'}
2025-06-04 04:41:13,614 - device_repair_system - ERROR - Server Error: {'timestamp': '2025-06-04T04:41:13.608905', 'status_code': 500, 'error_type': 'BuildError', 'error_message': "Could not build url for endpoint 'auth.login'. Did you mean 'users.index' instead?", 'url': 'http://127.0.0.1:5000/inventory/', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous', 'traceback': 'Traceback (most recent call last):\n  File "C:\\Program Files\\Python311\\Lib\\site-packages\\flask\\app.py", line 917, in full_dispatch_request\n    rv = self.dispatch_request()\n         ^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Program Files\\Python311\\Lib\\site-packages\\flask\\app.py", line 902, in dispatch_request\n    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Program Files\\Python311\\Lib\\site-packages\\flask_login\\utils.py", line 285, in decorated_view\n    return current_app.login_manager.unauthorized()\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Program Files\\Python311\\Lib\\site-packages\\flask_login\\login_manager.py", line 196, in unauthorized\n    redirect_url = make_login_url(login_view, next_url=request.url)\n                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Program Files\\Python311\\Lib\\site-packages\\flask_login\\utils.py", line 120, in login_url\n    base = expand_login_view(login_view)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Program Files\\Python311\\Lib\\site-packages\\flask_login\\utils.py", line 97, in expand_login_view\n    return url_for(login_view)\n           ^^^^^^^^^^^^^^^^^^^\n  File "C:\\Program Files\\Python311\\Lib\\site-packages\\flask\\helpers.py", line 232, in url_for\n    return current_app.url_for(\n           ^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Program Files\\Python311\\Lib\\site-packages\\flask\\app.py", line 1121, in url_for\n    return self.handle_url_build_error(error, endpoint, values)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Program Files\\Python311\\Lib\\site-packages\\flask\\app.py", line 1110, in url_for\n    rv = url_adapter.build(  # type: ignore[union-attr]\n         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Program Files\\Python311\\Lib\\site-packages\\werkzeug\\routing\\map.py", line 924, in build\n    raise BuildError(endpoint, values, method, self)\nwerkzeug.routing.exceptions.BuildError: Could not build url for endpoint \'auth.login\'. Did you mean \'users.index\' instead?\n'}
2025-06-04 04:45:44,257 - device_repair_system - INFO - Error handling system initialized
2025-06-04 04:47:28,299 - device_repair_system - INFO - Error handling system initialized
2025-06-04 04:49:20,713 - device_repair_system - INFO - Error handling system initialized
2025-06-04 04:50:00,796 - device_repair_system - ERROR - Server Error: {'timestamp': '2025-06-04T04:50:00.774799', 'status_code': 500, 'error_type': 'InvalidRequestError', 'error_message': "One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[UsedPart(used_part)]'. Original exception was: When initializing mapper Mapper[UsedPart(used_part)], expression 'InventoryItem' failed to locate a name ('InventoryItem'). If this is a class name, consider adding this relationship() to the <class 'app.models.device.UsedPart'> class after both dependent classes have been defined.", 'url': 'http://127.0.0.1:5000/inventory/', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous', 'traceback': 'Traceback (most recent call last):\n  File "C:\\Program Files\\Python311\\Lib\\site-packages\\flask\\app.py", line 917, in full_dispatch_request\n    rv = self.dispatch_request()\n         ^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Program Files\\Python311\\Lib\\site-packages\\flask\\app.py", line 902, in dispatch_request\n    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "D:\\device repair manaement system - Copy\\app\\routes\\inventory_enhanced.py", line 37, in index\n    query = SparePart.query\n            ^^^^^^^^^^^^^^^\n  File "C:\\Program Files\\Python311\\Lib\\site-packages\\flask_sqlalchemy\\model.py", line 22, in __get__\n    return cls.query_class(\n           ^^^^^^^^^^^^^^^^\n  File "C:\\Program Files\\Python311\\Lib\\site-packages\\sqlalchemy\\orm\\query.py", line 276, in __init__\n    self._set_entities(entities)\n  File "C:\\Program Files\\Python311\\Lib\\site-packages\\sqlalchemy\\orm\\query.py", line 288, in _set_entities\n    self._raw_columns = [\n                        ^\n  File "C:\\Program Files\\Python311\\Lib\\site-packages\\sqlalchemy\\orm\\query.py", line 289, in <listcomp>\n    coercions.expect(\n  File "C:\\Program Files\\Python311\\Lib\\site-packages\\sqlalchemy\\sql\\coercions.py", line 388, in expect\n    insp._post_inspect\n  File "C:\\Program Files\\Python311\\Lib\\site-packages\\sqlalchemy\\util\\langhelpers.py", line 1338, in __get__\n    obj.__dict__[self.__name__] = result = self.fget(obj)\n                                           ^^^^^^^^^^^^^^\n  File "C:\\Program Files\\Python311\\Lib\\site-packages\\sqlalchemy\\orm\\mapper.py", line 2724, in _post_inspect\n    self._check_configure()\n  File "C:\\Program Files\\Python311\\Lib\\site-packages\\sqlalchemy\\orm\\mapper.py", line 2401, in _check_configure\n    _configure_registries({self.registry}, cascade=True)\n  File "C:\\Program Files\\Python311\\Lib\\site-packages\\sqlalchemy\\orm\\mapper.py", line 4214, in _configure_registries\n    _do_configure_registries(registries, cascade)\n  File "C:\\Program Files\\Python311\\Lib\\site-packages\\sqlalchemy\\orm\\mapper.py", line 4251, in _do_configure_registries\n    raise e\nsqlalchemy.exc.InvalidRequestError: One or more mappers failed to initialize - can\'t proceed with initialization of other mappers. Triggering mapper: \'Mapper[UsedPart(used_part)]\'. Original exception was: When initializing mapper Mapper[UsedPart(used_part)], expression \'InventoryItem\' failed to locate a name (\'InventoryItem\'). If this is a class name, consider adding this relationship() to the <class \'app.models.device.UsedPart\'> class after both dependent classes have been defined.\n'}
2025-06-04 04:54:56,490 - device_repair_system - INFO - Error handling system initialized
2025-06-04 04:55:06,646 - device_repair_system - WARNING - Client Error: {'timestamp': '2025-06-04T04:55:06.645026', 'status_code': 404, 'error_type': 'NotFound', 'error_message': '404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.', 'url': 'http://127.0.0.1:5000/tenant/register', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous'}
2025-06-04 04:55:38,130 - device_repair_system - ERROR - Server Error: {'timestamp': '2025-06-04T04:55:38.120805', 'status_code': 500, 'error_type': 'InvalidRequestError', 'error_message': "One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[UsedPart(used_part)]'. Original exception was: Could not determine join condition between parent/child tables on relationship UsedPart.part - there are no foreign keys linking these tables.  Ensure that referencing columns are associated with a ForeignKey or ForeignKeyConstraint, or specify a 'primaryjoin' expression.", 'url': 'http://127.0.0.1:5000/inventory/', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous', 'traceback': 'Traceback (most recent call last):\n  File "C:\\Program Files\\Python311\\Lib\\site-packages\\flask\\app.py", line 917, in full_dispatch_request\n    rv = self.dispatch_request()\n         ^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Program Files\\Python311\\Lib\\site-packages\\flask\\app.py", line 902, in dispatch_request\n    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "D:\\device repair manaement system - Copy\\app\\routes\\inventory_enhanced.py", line 37, in index\n    query = SparePart.query\n            ^^^^^^^^^^^^^^^\n  File "C:\\Program Files\\Python311\\Lib\\site-packages\\flask_sqlalchemy\\model.py", line 22, in __get__\n    return cls.query_class(\n           ^^^^^^^^^^^^^^^^\n  File "C:\\Program Files\\Python311\\Lib\\site-packages\\sqlalchemy\\orm\\query.py", line 276, in __init__\n    self._set_entities(entities)\n  File "C:\\Program Files\\Python311\\Lib\\site-packages\\sqlalchemy\\orm\\query.py", line 288, in _set_entities\n    self._raw_columns = [\n                        ^\n  File "C:\\Program Files\\Python311\\Lib\\site-packages\\sqlalchemy\\orm\\query.py", line 289, in <listcomp>\n    coercions.expect(\n  File "C:\\Program Files\\Python311\\Lib\\site-packages\\sqlalchemy\\sql\\coercions.py", line 388, in expect\n    insp._post_inspect\n  File "C:\\Program Files\\Python311\\Lib\\site-packages\\sqlalchemy\\util\\langhelpers.py", line 1338, in __get__\n    obj.__dict__[self.__name__] = result = self.fget(obj)\n                                           ^^^^^^^^^^^^^^\n  File "C:\\Program Files\\Python311\\Lib\\site-packages\\sqlalchemy\\orm\\mapper.py", line 2724, in _post_inspect\n    self._check_configure()\n  File "C:\\Program Files\\Python311\\Lib\\site-packages\\sqlalchemy\\orm\\mapper.py", line 2401, in _check_configure\n    _configure_registries({self.registry}, cascade=True)\n  File "C:\\Program Files\\Python311\\Lib\\site-packages\\sqlalchemy\\orm\\mapper.py", line 4214, in _configure_registries\n    _do_configure_registries(registries, cascade)\n  File "C:\\Program Files\\Python311\\Lib\\site-packages\\sqlalchemy\\orm\\mapper.py", line 4251, in _do_configure_registries\n    raise e\nsqlalchemy.exc.InvalidRequestError: One or more mappers failed to initialize - can\'t proceed with initialization of other mappers. Triggering mapper: \'Mapper[UsedPart(used_part)]\'. Original exception was: Could not determine join condition between parent/child tables on relationship UsedPart.part - there are no foreign keys linking these tables.  Ensure that referencing columns are associated with a ForeignKey or ForeignKeyConstraint, or specify a \'primaryjoin\' expression.\n'}
2025-06-04 04:55:38,592 - device_repair_system - WARNING - Client Error: {'timestamp': '2025-06-04T04:55:38.592478', 'status_code': 404, 'error_type': 'NotFound', 'error_message': '404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.', 'url': 'http://127.0.0.1:5000/favicon.ico', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous'}
2025-06-04 04:55:56,433 - device_repair_system - WARNING - Client Error: {'timestamp': '2025-06-04T04:55:56.433418', 'status_code': 404, 'error_type': 'NotFound', 'error_message': '404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.', 'url': 'http://127.0.0.1:5000/tenant/register', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous'}
2025-06-04 04:58:28,265 - device_repair_system - INFO - Error handling system initialized
2025-06-04 04:58:37,166 - device_repair_system - WARNING - Client Error: {'timestamp': '2025-06-04T04:58:37.166488', 'status_code': 404, 'error_type': 'NotFound', 'error_message': '404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.', 'url': 'http://127.0.0.1:5000/tenant/register', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous'}
2025-06-04 05:00:27,016 - device_repair_system - INFO - Error handling system initialized
2025-06-04 05:00:28,508 - device_repair_system - ERROR - Server Error: {'timestamp': '2025-06-04T05:00:28.501901', 'status_code': 500, 'error_type': 'InvalidRequestError', 'error_message': "One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[Invoice(invoice)]'. Original exception was: Could not determine join condition between parent/child tables on relationship Invoice.payment - there are no foreign keys linking these tables.  Ensure that referencing columns are associated with a ForeignKey or ForeignKeyConstraint, or specify a 'primaryjoin' expression.", 'url': 'http://127.0.0.1:5000/inventory/', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous', 'traceback': 'Traceback (most recent call last):\n  File "C:\\Program Files\\Python311\\Lib\\site-packages\\flask\\app.py", line 917, in full_dispatch_request\n    rv = self.dispatch_request()\n         ^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Program Files\\Python311\\Lib\\site-packages\\flask\\app.py", line 902, in dispatch_request\n    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "D:\\device repair manaement system - Copy\\app\\routes\\inventory_enhanced.py", line 37, in index\n    query = SparePart.query\n            ^^^^^^^^^^^^^^^\n  File "C:\\Program Files\\Python311\\Lib\\site-packages\\flask_sqlalchemy\\model.py", line 22, in __get__\n    return cls.query_class(\n           ^^^^^^^^^^^^^^^^\n  File "C:\\Program Files\\Python311\\Lib\\site-packages\\sqlalchemy\\orm\\query.py", line 276, in __init__\n    self._set_entities(entities)\n  File "C:\\Program Files\\Python311\\Lib\\site-packages\\sqlalchemy\\orm\\query.py", line 288, in _set_entities\n    self._raw_columns = [\n                        ^\n  File "C:\\Program Files\\Python311\\Lib\\site-packages\\sqlalchemy\\orm\\query.py", line 289, in <listcomp>\n    coercions.expect(\n  File "C:\\Program Files\\Python311\\Lib\\site-packages\\sqlalchemy\\sql\\coercions.py", line 388, in expect\n    insp._post_inspect\n  File "C:\\Program Files\\Python311\\Lib\\site-packages\\sqlalchemy\\util\\langhelpers.py", line 1338, in __get__\n    obj.__dict__[self.__name__] = result = self.fget(obj)\n                                           ^^^^^^^^^^^^^^\n  File "C:\\Program Files\\Python311\\Lib\\site-packages\\sqlalchemy\\orm\\mapper.py", line 2724, in _post_inspect\n    self._check_configure()\n  File "C:\\Program Files\\Python311\\Lib\\site-packages\\sqlalchemy\\orm\\mapper.py", line 2401, in _check_configure\n    _configure_registries({self.registry}, cascade=True)\n  File "C:\\Program Files\\Python311\\Lib\\site-packages\\sqlalchemy\\orm\\mapper.py", line 4214, in _configure_registries\n    _do_configure_registries(registries, cascade)\n  File "C:\\Program Files\\Python311\\Lib\\site-packages\\sqlalchemy\\orm\\mapper.py", line 4251, in _do_configure_registries\n    raise e\nsqlalchemy.exc.InvalidRequestError: One or more mappers failed to initialize - can\'t proceed with initialization of other mappers. Triggering mapper: \'Mapper[Invoice(invoice)]\'. Original exception was: Could not determine join condition between parent/child tables on relationship Invoice.payment - there are no foreign keys linking these tables.  Ensure that referencing columns are associated with a ForeignKey or ForeignKeyConstraint, or specify a \'primaryjoin\' expression.\n'}
2025-06-04 05:00:35,738 - device_repair_system - WARNING - Client Error: {'timestamp': '2025-06-04T05:00:35.737362', 'status_code': 404, 'error_type': 'NotFound', 'error_message': '404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.', 'url': 'http://127.0.0.1:5000/tenant/register', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous'}
2025-06-04 05:03:22,035 - device_repair_system - INFO - Error handling system initialized
2025-06-04 05:03:42,871 - device_repair_system - WARNING - Client Error: {'timestamp': '2025-06-04T05:03:42.870842', 'status_code': 404, 'error_type': 'NotFound', 'error_message': '404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.', 'url': 'http://127.0.0.1:5000/tenant/register', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous'}
2025-06-04 05:04:42,658 - device_repair_system - INFO - Error handling system initialized
2025-06-04 05:04:53,436 - device_repair_system - WARNING - Client Error: {'timestamp': '2025-06-04T05:04:53.435254', 'status_code': 404, 'error_type': 'NotFound', 'error_message': '404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.', 'url': 'http://127.0.0.1:5000/tenant/register', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous'}
2025-06-04 05:08:29,555 - device_repair_system - INFO - Error handling system initialized
2025-06-04 05:08:35,666 - device_repair_system - WARNING - Client Error: {'timestamp': '2025-06-04T05:08:35.665111', 'status_code': 404, 'error_type': 'NotFound', 'error_message': '404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.', 'url': 'http://127.0.0.1:5000/favicon.ico', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous'}
2025-06-04 05:08:37,722 - device_repair_system - WARNING - Client Error: {'timestamp': '2025-06-04T05:08:37.721325', 'status_code': 404, 'error_type': 'NotFound', 'error_message': '404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.', 'url': 'http://127.0.0.1:5000/tenant/register', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous'}
2025-06-04 05:08:55,941 - device_repair_system - ERROR - Server Error: {'timestamp': '2025-06-04T05:08:55.936436', 'status_code': 500, 'error_type': 'AttributeError', 'error_message': "type object 'SparePart' has no attribute 'price'", 'url': 'http://127.0.0.1:5000/inventory/', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous', 'traceback': 'Traceback (most recent call last):\n  File "C:\\Program Files\\Python311\\Lib\\site-packages\\flask\\app.py", line 917, in full_dispatch_request\n    rv = self.dispatch_request()\n         ^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Program Files\\Python311\\Lib\\site-packages\\flask\\app.py", line 902, in dispatch_request\n    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "D:\\device repair manaement system - Copy\\app\\routes\\inventory_enhanced.py", line 109, in index\n    total_value = db.session.query(func.sum(SparePart.price * SparePart.quantity)).scalar() or 0\n                                            ^^^^^^^^^^^^^^^\nAttributeError: type object \'SparePart\' has no attribute \'price\'\n'}
2025-06-04 05:10:02,849 - device_repair_system - WARNING - Client Error: {'timestamp': '2025-06-04T05:10:02.848789', 'status_code': 404, 'error_type': 'NotFound', 'error_message': '404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.', 'url': 'http://127.0.0.1:5000/tenant/register', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous'}
2025-06-04 05:16:04,726 - device_repair_system - INFO - Error handling system initialized
2025-06-04 05:16:35,820 - device_repair_system - WARNING - Client Error: {'timestamp': '2025-06-04T05:16:35.820381', 'status_code': 404, 'error_type': 'NotFound', 'error_message': '404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.', 'url': 'http://127.0.0.1:5000/favicon.ico', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous'}
2025-06-04 05:16:49,756 - device_repair_system - WARNING - Client Error: {'timestamp': '2025-06-04T05:16:49.756040', 'status_code': 404, 'error_type': 'NotFound', 'error_message': '404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.', 'url': 'http://127.0.0.1:5000/tenant/register', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous'}
2025-06-04 05:31:09,293 - device_repair_system - INFO - Error handling system initialized
2025-06-04 05:31:28,672 - device_repair_system - WARNING - Client Error: {'timestamp': '2025-06-04T05:31:28.672737', 'status_code': 404, 'error_type': 'NotFound', 'error_message': '404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.', 'url': 'http://127.0.0.1:5000/tenant/register', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous'}
2025-06-04 05:31:38,699 - device_repair_system - WARNING - Client Error: {'timestamp': '2025-06-04T05:31:38.698737', 'status_code': 404, 'error_type': 'NotFound', 'error_message': '404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.', 'url': 'http://127.0.0.1:5000/tenant/register', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous'}
2025-06-04 05:33:20,240 - device_repair_system - INFO - Error handling system initialized
2025-06-04 05:33:44,224 - device_repair_system - INFO - Error handling system initialized
2025-06-04 05:40:49,132 - device_repair_system - INFO - Error handling system initialized
2025-06-04 05:41:04,488 - device_repair_system - WARNING - Client Error: {'timestamp': '2025-06-04T05:41:04.488684', 'status_code': 404, 'error_type': 'NotFound', 'error_message': '404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.', 'url': 'http://127.0.0.1:5000/favicon.ico', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous'}
2025-06-04 05:41:05,180 - device_repair_system - WARNING - Client Error: {'timestamp': '2025-06-04T05:41:05.180414', 'status_code': 404, 'error_type': 'NotFound', 'error_message': '404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.', 'url': 'http://127.0.0.1:5000/tenant/register', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous'}
2025-06-04 05:45:58,800 - device_repair_system - INFO - Error handling system initialized
2025-06-04 05:46:48,564 - device_repair_system - WARNING - Client Error: {'timestamp': '2025-06-04T05:46:48.564738', 'status_code': 404, 'error_type': 'NotFound', 'error_message': '404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.', 'url': 'http://127.0.0.1:5000/tenant/register', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous'}
2025-06-04 05:47:25,397 - device_repair_system - WARNING - Client Error: {'timestamp': '2025-06-04T05:47:25.397347', 'status_code': 404, 'error_type': 'NotFound', 'error_message': '404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.', 'url': 'http://127.0.0.1:5000/tenant/register', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous'}
2025-06-04 05:47:36,069 - device_repair_system - WARNING - Client Error: {'timestamp': '2025-06-04T05:47:36.069613', 'status_code': 404, 'error_type': 'NotFound', 'error_message': '404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.', 'url': 'http://127.0.0.1:5000/tenant/register', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous'}
2025-06-04 05:47:41,761 - device_repair_system - WARNING - Client Error: {'timestamp': '2025-06-04T05:47:41.761095', 'status_code': 404, 'error_type': 'NotFound', 'error_message': '404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.', 'url': 'http://127.0.0.1:5000/support/chat', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous'}
2025-06-04 05:51:27,166 - device_repair_system - WARNING - Client Error: {'timestamp': '2025-06-04T05:51:27.166209', 'status_code': 404, 'error_type': 'NotFound', 'error_message': '404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.', 'url': 'http://127.0.0.1:5000/tenant/register', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous'}
2025-06-04 05:53:47,733 - device_repair_system - INFO - Error handling system initialized
2025-06-04 05:57:18,687 - device_repair_system - INFO - Error handling system initialized
2025-06-04 06:06:30,340 - device_repair_system - INFO - Error handling system initialized
2025-06-04 06:09:14,862 - device_repair_system - INFO - Error handling system initialized
2025-06-04 06:11:57,035 - device_repair_system - INFO - Error handling system initialized
2025-06-04 06:12:59,885 - device_repair_system - INFO - Error handling system initialized
2025-06-04 06:20:44,095 - device_repair_system - WARNING - Client Error: {'timestamp': '2025-06-04T06:20:44.095846', 'status_code': 404, 'error_type': 'NotFound', 'error_message': '404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.', 'url': 'http://127.0.0.1:5000/tenant', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous'}
2025-06-04 06:21:08,435 - device_repair_system - INFO - Error handling system initialized
2025-06-04 06:21:14,275 - device_repair_system - ERROR - Server Error: {'timestamp': '2025-06-04T06:21:14.272415', 'status_code': 500, 'error_type': 'ModuleNotFoundError', 'error_message': "No module named 'psutil'", 'url': 'http://127.0.0.1:5000/system/info', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous', 'traceback': 'Traceback (most recent call last):\n  File "D:\\device repair manaement system - Copy\\venv\\Lib\\site-packages\\flask\\app.py", line 1484, in full_dispatch_request\n    rv = self.dispatch_request()\n         ^^^^^^^^^^^^^^^^^^^^^^^\n  File "D:\\device repair manaement system - Copy\\venv\\Lib\\site-packages\\flask\\app.py", line 1469, in dispatch_request\n    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "D:\\device repair manaement system - Copy\\app\\routes\\main.py", line 59, in system_info\n    import psutil\nModuleNotFoundError: No module named \'psutil\'\n'}
2025-06-04 06:23:07,702 - device_repair_system - INFO - Error handling system initialized
2025-06-04 06:23:30,983 - device_repair_system - WARNING - Client Error: {'timestamp': '2025-06-04T06:23:30.982209', 'status_code': 404, 'error_type': 'NotFound', 'error_message': '404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.', 'url': 'http://127.0.0.1:5000/admin', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous'}
2025-06-04 06:24:35,501 - device_repair_system - INFO - Error handling system initialized
2025-06-04 06:24:48,442 - device_repair_system - WARNING - Client Error: {'timestamp': '2025-06-04T06:24:48.442186', 'status_code': 404, 'error_type': 'NotFound', 'error_message': '404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.', 'url': 'http://127.0.0.1:5000/admin', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous'}
2025-06-04 06:26:28,927 - device_repair_system - WARNING - Client Error: {'timestamp': '2025-06-04T06:26:28.927616', 'status_code': 404, 'error_type': 'NotFound', 'error_message': '404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.', 'url': 'http://127.0.0.1:5000/admin', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous'}
2025-06-04 06:27:39,903 - device_repair_system - WARNING - Client Error: {'timestamp': '2025-06-04T06:27:39.903691', 'status_code': 404, 'error_type': 'NotFound', 'error_message': '404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.', 'url': 'http://127.0.0.1:5000/admin', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous'}
2025-06-04 06:31:59,261 - device_repair_system - WARNING - Client Error: {'timestamp': '2025-06-04T06:31:59.261678', 'status_code': 404, 'error_type': 'NotFound', 'error_message': '404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.', 'url': 'http://127.0.0.1:5000/admin', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous'}
2025-06-04 06:47:40,116 - device_repair_system - INFO - Error handling system initialized
2025-06-04 06:49:40,969 - device_repair_system - INFO - Error handling system initialized
2025-06-04 06:52:03,225 - device_repair_system - INFO - Error handling system initialized
2025-06-04 06:52:04,501 - device_repair_system - WARNING - Client Error: {'timestamp': '2025-06-04T06:52:04.500355', 'status_code': 404, 'error_type': 'NotFound', 'error_message': '404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.', 'url': 'http://127.0.0.1:5000/admin', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous'}
2025-06-04 06:53:36,751 - device_repair_system - INFO - Error handling system initialized
2025-06-04 06:54:03,110 - device_repair_system - WARNING - Client Error: {'timestamp': '2025-06-04T06:54:03.110634', 'status_code': 404, 'error_type': 'NotFound', 'error_message': '404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.', 'url': 'http://127.0.0.1:5000/system', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous'}
2025-06-04 06:54:22,291 - device_repair_system - WARNING - Client Error: {'timestamp': '2025-06-04T06:54:22.291856', 'status_code': 404, 'error_type': 'NotFound', 'error_message': '404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.', 'url': 'http://127.0.0.1:5000/admin', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous'}
2025-06-04 06:57:34,332 - device_repair_system - INFO - Error handling system initialized
2025-06-04 06:57:42,155 - device_repair_system - WARNING - Client Error: {'timestamp': '2025-06-04T06:57:42.155091', 'status_code': 404, 'error_type': 'NotFound', 'error_message': '404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.', 'url': 'http://127.0.0.1:5000/admin', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous'}
2025-06-04 06:57:52,283 - device_repair_system - INFO - Error handling system initialized
2025-06-04 07:01:14,036 - device_repair_system - INFO - Error handling system initialized
2025-06-04 07:02:02,316 - device_repair_system - INFO - Error handling system initialized
2025-06-04 07:02:26,742 - device_repair_system - WARNING - Client Error: {'timestamp': '2025-06-04T07:02:26.741788', 'status_code': 404, 'error_type': 'NotFound', 'error_message': '404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.', 'url': 'http://***************:5000/admin', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '***************', 'user_id': 'Anonymous'}
2025-06-04 07:03:09,889 - device_repair_system - INFO - Error handling system initialized
2025-06-04 07:08:22,870 - device_repair_system - INFO - Error handling system initialized
2025-06-04 07:11:11,419 - device_repair_system - INFO - Error handling system initialized
2025-06-04 07:11:19,803 - device_repair_system - ERROR - Server Error: {'timestamp': '2025-06-04T07:11:19.716521', 'status_code': 500, 'error_type': 'BuildError', 'error_message': "Could not build url for endpoint 'auth.login'. Did you mean 'dashboard.index' instead?", 'url': 'http://127.0.0.1:5000/', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous', 'traceback': 'Traceback (most recent call last):\n  File "C:\\Program Files\\Python311\\Lib\\site-packages\\flask\\app.py", line 917, in full_dispatch_request\n    rv = self.dispatch_request()\n         ^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Program Files\\Python311\\Lib\\site-packages\\flask\\app.py", line 902, in dispatch_request\n    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "D:\\device repair manaement system - Copy\\app\\routes\\main.py", line 17, in index\n    return redirect(url_for(\'auth.login\'))\n                    ^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Program Files\\Python311\\Lib\\site-packages\\flask\\helpers.py", line 232, in url_for\n    return current_app.url_for(\n           ^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Program Files\\Python311\\Lib\\site-packages\\flask\\app.py", line 1121, in url_for\n    return self.handle_url_build_error(error, endpoint, values)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Program Files\\Python311\\Lib\\site-packages\\flask\\app.py", line 1110, in url_for\n    rv = url_adapter.build(  # type: ignore[union-attr]\n         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Program Files\\Python311\\Lib\\site-packages\\werkzeug\\routing\\map.py", line 924, in build\n    raise BuildError(endpoint, values, method, self)\nwerkzeug.routing.exceptions.BuildError: Could not build url for endpoint \'auth.login\'. Did you mean \'dashboard.index\' instead?\n'}
2025-06-04 07:13:28,078 - device_repair_system - INFO - Error handling system initialized
2025-06-04 07:20:52,498 - device_repair_system - INFO - Error handling system initialized
2025-06-04 07:28:30,362 - device_repair_system - INFO - Error handling system initialized
