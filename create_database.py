#!/usr/bin/env python3
"""
Create SQLite Database for Device Repair Management System
This script creates a complete database with all necessary tables and sample data
"""

import sqlite3
import os
import hashlib
from datetime import datetime, timedelta
import json

def create_password_hash(password):
    """Create a password hash using SHA-256"""
    return hashlib.sha256(password.encode()).hexdigest()

def create_database():
    """Create the SQLite database with all necessary tables"""
    
    # Remove existing database if it exists
    db_path = 'device_repair_system.db'
    if os.path.exists(db_path):
        os.remove(db_path)
        print(f"🗑️ Removed existing database: {db_path}")
    
    # Create new database connection
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    print(f"📊 Creating database: {db_path}")
    
    # Enable foreign key constraints
    cursor.execute("PRAGMA foreign_keys = ON")
    
    # Create Users table
    cursor.execute('''
    CREATE TABLE users (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        username VARCHAR(80) UNIQUE NOT NULL,
        email VARCHAR(120) UNIQUE NOT NULL,
        password_hash VARCHAR(255) NOT NULL,
        full_name VARCHAR(200),
        phone VARCHAR(20),
        role VARCHAR(20) NOT NULL DEFAULT 'customer',
        is_active BOOLEAN DEFAULT 1,
        is_admin BOOLEAN DEFAULT 0,
        tenant_id INTEGER DEFAULT 1,
        telegram_chat_id VARCHAR(50),
        notifications_enabled BOOLEAN DEFAULT 1,
        notify_new_device BOOLEAN DEFAULT 1,
        notify_status_change BOOLEAN DEFAULT 1,
        notify_repair_complete BOOLEAN DEFAULT 1,
        is_trial_account BOOLEAN DEFAULT 0,
        trial_start_date DATETIME,
        trial_end_date DATETIME,
        trial_expired BOOLEAN DEFAULT 0,
        license_type VARCHAR(50) DEFAULT 'free',
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        last_login DATETIME
    )
    ''')
    
    # Create Devices table
    cursor.execute('''
    CREATE TABLE devices (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        type VARCHAR(50) NOT NULL,
        model VARCHAR(100) NOT NULL,
        color VARCHAR(50),
        serial_number VARCHAR(100) UNIQUE,
        password VARCHAR(100),
        customer_id INTEGER,
        technician_id INTEGER,
        status VARCHAR(20) DEFAULT 'receiving',
        owner_name VARCHAR(200),
        notes TEXT,
        repair_completed_at DATETIME,
        tenant_id INTEGER DEFAULT 1,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        scheduled_for_deletion BOOLEAN DEFAULT 0,
        is_soft_deleted BOOLEAN DEFAULT 0,
        soft_deleted_at DATETIME,
        deletion_reason TEXT,
        FOREIGN KEY (customer_id) REFERENCES users (id),
        FOREIGN KEY (technician_id) REFERENCES users (id)
    )
    ''')
    
    # Create Repair Tickets table
    cursor.execute('''
    CREATE TABLE repair_tickets (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        device_id INTEGER NOT NULL,
        issue_description TEXT NOT NULL,
        diagnosis TEXT,
        repair_notes TEXT,
        status VARCHAR(20) DEFAULT 'received',
        priority VARCHAR(20) DEFAULT 'normal',
        cost DECIMAL(10,2) DEFAULT 0.00,
        payment_status VARCHAR(20) DEFAULT 'pending',
        estimated_completion DATETIME,
        actual_completion DATETIME,
        warranty_period INTEGER DEFAULT 30,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (device_id) REFERENCES devices (id) ON DELETE CASCADE
    )
    ''')
    
    # Create Inventory Items table
    cursor.execute('''
    CREATE TABLE inventory_items (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name VARCHAR(200) NOT NULL,
        description TEXT,
        category VARCHAR(100),
        sku VARCHAR(50) UNIQUE,
        quantity INTEGER DEFAULT 0,
        min_quantity INTEGER DEFAULT 5,
        unit_price DECIMAL(10,2) DEFAULT 0.00,
        supplier VARCHAR(200),
        location VARCHAR(100),
        tenant_id INTEGER DEFAULT 1,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )
    ''')
    
    # Create System Settings table
    cursor.execute('''
    CREATE TABLE system_settings (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        setting_key VARCHAR(100) UNIQUE NOT NULL,
        setting_value TEXT,
        setting_type VARCHAR(20) DEFAULT 'string',
        description TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )
    ''')
    
    # Create Tenants table
    cursor.execute('''
    CREATE TABLE tenants (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name VARCHAR(200) NOT NULL,
        domain VARCHAR(100),
        is_active BOOLEAN DEFAULT 1,
        database_name VARCHAR(100),
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )
    ''')
    
    # Create Activations table
    cursor.execute('''
    CREATE TABLE activations (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        code VARCHAR(50) UNIQUE NOT NULL,
        is_used BOOLEAN DEFAULT 0,
        used_by INTEGER,
        used_at DATETIME,
        expires_at DATETIME,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (used_by) REFERENCES users (id)
    )
    ''')
    
    print("✅ Database tables created successfully")
    
    return conn, cursor

def populate_sample_data(conn, cursor):
    """Populate the database with sample data"""
    
    print("📝 Populating database with sample data...")
    
    # Insert default tenant
    cursor.execute('''
    INSERT INTO tenants (name, domain, database_name) 
    VALUES ('Default Company', 'localhost', 'device_repair_system.db')
    ''')
    
    # Insert default users
    users_data = [
        ('admin', '<EMAIL>', create_password_hash('admin123'), 'System Administrator', '+966501234567', 'admin', 1, 1, 1),
        ('technician', '<EMAIL>', create_password_hash('tech123'), 'Default Technician', '+966501234568', 'technician', 1, 0, 1),
        ('customer', '<EMAIL>', create_password_hash('customer123'), 'Test Customer', '+966501234569', 'customer', 1, 0, 1)
    ]
    
    cursor.executemany('''
    INSERT INTO users (username, email, password_hash, full_name, phone, role, is_active, is_admin, tenant_id)
    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
    ''', users_data)
    
    # Insert sample devices
    devices_data = [
        ('iPhone', '14 Pro Max', 'Deep Purple', 'SAMPLE001', 'محمي', 3, 2, 'receiving', 'أحمد محمد العلي', 'الشاشة مكسورة والبطارية تفرغ بسرعة', 1),
        ('Samsung', 'Galaxy S23 Ultra', 'Phantom Black', 'SAMPLE002', 'محمي', 3, 2, 'in_repair', 'فاطمة أحمد السعودي', 'مشكلة في الكاميرا الخلفية', 1),
        ('iPad', 'Air 5th Gen', 'Space Gray', 'SAMPLE003', 'محمي', 3, None, 'receiving', 'محمد عبدالله الخالد', 'الشاشة لا تستجيب للمس', 1)
    ]
    
    cursor.executemany('''
    INSERT INTO devices (type, model, color, serial_number, password, customer_id, technician_id, status, owner_name, notes, tenant_id)
    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    ''', devices_data)
    
    # Insert sample repair tickets
    repair_tickets_data = [
        (1, 'الشاشة مكسورة مع خطوط سوداء، البطارية تفرغ بسرعة', 'يحتاج تغيير شاشة وبطارية', 'تم استلام الجهاز وفحصه مبدئياً', 'received', 'high', 450.00, 'pending'),
        (2, 'الكاميرا الخلفية لا تعمل، تظهر شاشة سوداء', 'مشكلة في وحدة الكاميرا', 'جاري العمل على إصلاح الكاميرا', 'in_progress', 'normal', 280.00, 'pending'),
        (3, 'الشاشة لا تستجيب للمس في بعض المناطق', 'مشكلة في محول اللمس', 'في انتظار قطع الغيار', 'received', 'normal', 320.00, 'pending')
    ]
    
    cursor.executemany('''
    INSERT INTO repair_tickets (device_id, issue_description, diagnosis, repair_notes, status, priority, cost, payment_status)
    VALUES (?, ?, ?, ?, ?, ?, ?, ?)
    ''', repair_tickets_data)
    
    # Insert sample inventory items
    inventory_data = [
        ('iPhone 14 Pro Max Screen', 'Original OLED display assembly', 'Screens', 'IP14PM-SCR', 5, 2, 380.00, 'Apple Authorized', 'Shelf A1'),
        ('Samsung S23 Ultra Camera Module', 'Rear camera assembly 200MP', 'Cameras', 'S23U-CAM', 3, 1, 250.00, 'Samsung Parts', 'Shelf B2'),
        ('iPad Air 5 Digitizer', 'Touch screen digitizer', 'Digitizers', 'IPA5-DIG', 4, 2, 180.00, 'OEM Supplier', 'Shelf C1'),
        ('Universal Battery 3000mAh', 'High capacity lithium battery', 'Batteries', 'BATT-3000', 10, 5, 45.00, 'Battery Plus', 'Shelf D1'),
        ('Precision Screwdriver Set', 'Professional repair tools', 'Tools', 'TOOL-SET1', 2, 1, 25.00, 'Tool Supply Co', 'Tool Cabinet')
    ]
    
    cursor.executemany('''
    INSERT INTO inventory_items (name, description, category, sku, quantity, min_quantity, unit_price, supplier, location)
    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
    ''', inventory_data)
    
    # Insert system settings
    settings_data = [
        ('company_name', 'مركز إصلاح الأجهزة المتقدم', 'string', 'Company name displayed in reports'),
        ('company_address', 'الرياض، المملكة العربية السعودية', 'string', 'Company address'),
        ('company_phone', '+966501234567', 'string', 'Company phone number'),
        ('company_email', '<EMAIL>', 'string', 'Company email address'),
        ('default_warranty_days', '30', 'integer', 'Default warranty period in days'),
        ('currency_symbol', 'ر.س', 'string', 'Currency symbol for pricing'),
        ('tax_rate', '15', 'decimal', 'Tax rate percentage'),
        ('notification_enabled', 'true', 'boolean', 'Enable system notifications'),
        ('auto_backup_enabled', 'true', 'boolean', 'Enable automatic database backup'),
        ('max_devices_per_customer', '10', 'integer', 'Maximum devices per customer')
    ]
    
    cursor.executemany('''
    INSERT INTO system_settings (setting_key, setting_value, setting_type, description)
    VALUES (?, ?, ?, ?)
    ''', settings_data)
    
    # Insert sample activation codes
    activation_codes = [
        'DEMO-2025-001',
        'DEMO-2025-002', 
        'DEMO-2025-003',
        'ADMIN-FULL-ACCESS',
        'TECH-LIMITED-001'
    ]
    
    for code in activation_codes:
        cursor.execute('''
        INSERT INTO activations (code, expires_at)
        VALUES (?, ?)
        ''', (code, datetime.now() + timedelta(days=365)))
    
    print("✅ Sample data inserted successfully")

def verify_database(conn, cursor):
    """Verify that the database was created correctly"""
    
    print("🔍 Verifying database integrity...")
    
    # Check table counts
    tables_to_check = [
        ('users', 3),
        ('devices', 3), 
        ('repair_tickets', 3),
        ('inventory_items', 5),
        ('system_settings', 10),
        ('tenants', 1),
        ('activations', 5)
    ]
    
    for table_name, expected_count in tables_to_check:
        cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
        actual_count = cursor.fetchone()[0]
        
        if actual_count >= expected_count:
            print(f"  ✅ {table_name}: {actual_count} records")
        else:
            print(f"  ❌ {table_name}: {actual_count} records (expected at least {expected_count})")
    
    # Test a sample query
    cursor.execute('''
    SELECT u.username, u.role, d.type, d.model, rt.status 
    FROM users u
    LEFT JOIN devices d ON u.id = d.customer_id
    LEFT JOIN repair_tickets rt ON d.id = rt.device_id
    WHERE u.role = 'customer'
    ''')
    
    results = cursor.fetchall()
    print(f"  ✅ Sample query returned {len(results)} results")
    
    print("✅ Database verification completed")

def main():
    """Main function to create and populate the database"""
    
    print("🚀 DEVICE REPAIR MANAGEMENT SYSTEM - DATABASE CREATION")
    print("=" * 70)
    print(f"⏰ Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    try:
        # Create database and tables
        conn, cursor = create_database()
        
        # Populate with sample data
        populate_sample_data(conn, cursor)
        
        # Verify database
        verify_database(conn, cursor)
        
        # Commit changes and close connection
        conn.commit()
        conn.close()
        
        print()
        print("=" * 70)
        print("🎉 DATABASE CREATION COMPLETED SUCCESSFULLY!")
        print("=" * 70)
        print("📊 Database file: device_repair_system.db")
        print("👤 Default users created:")
        print("   🔑 Admin: username=admin, password=admin123")
        print("   🔧 Technician: username=technician, password=tech123") 
        print("   👥 Customer: username=customer, password=customer123")
        print()
        print("📱 Sample devices and repair tickets added")
        print("📦 Sample inventory items added")
        print("⚙️ System settings configured")
        print("🎫 Activation codes generated")
        print()
        print("🌐 The database is ready for use with the Flask application!")
        print("=" * 70)
        
        return True
        
    except Exception as e:
        print(f"❌ Error creating database: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    main()
