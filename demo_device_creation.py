#!/usr/bin/env python3
"""
Device Creation Demo
Demonstrates the core functionality of the device repair management system
"""

import sys
import os
from datetime import datetime

# Add the app directory to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def demo_device_creation():
    """Demonstrate device creation and Telegram notifications"""
    print("🚀 DEVICE REPAIR MANAGEMENT SYSTEM - DEMO")
    print("=" * 60)
    print(f"⏰ Demo started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    try:
        from app import create_app, db
        from app.models.device import Device
        from app.models.repair_ticket import RepairTicket
        from app.models.user import User
        from app.services.telegram_service import TelegramService
        
        app = create_app()
        
        with app.app_context():
            print("💾 Database Connection Test...")
            
            # Check current state
            device_count = Device.query.count()
            user_count = User.query.count()
            
            print(f"✅ Database connected successfully")
            print(f"   Current devices: {device_count}")
            print(f"   Current users: {user_count}")
            print()
            
            print("📱 Creating New Device...")
            
            # Create a sample device
            new_device = Device(
                type='Samsung',
                model='Galaxy S23 Ultra',
                color='Phantom Black',
                serial_number='DEMO' + datetime.now().strftime('%Y%m%d%H%M%S'),
                password='محمي',
                owner_name='محمد أحمد السعودي',
                status='receiving',
                notes='Owner Phone: +966501234567\nDevice has screen damage and battery issues',
                tenant_id=1
            )
            
            db.session.add(new_device)
            db.session.flush()
            
            print(f"✅ Device created successfully!")
            print(f"   Device ID: {new_device.id}")
            print(f"   Type: {new_device.type} {new_device.model}")
            print(f"   Color: {new_device.color}")
            print(f"   Serial: {new_device.serial_number}")
            print(f"   Owner: {new_device.owner_name}")
            print(f"   Status: {new_device.status}")
            print()
            
            print("🎫 Creating Repair Ticket...")
            
            # Create repair ticket
            repair_ticket = RepairTicket(
                device_id=new_device.id,
                issue_description='الشاشة مكسورة مع خطوط سوداء، البطارية تفرغ بسرعة، الجهاز يسخن بشكل غير طبيعي',
                status='received',
                priority='high',
                diagnosis='يحتاج تغيير شاشة وبطارية',
                repair_notes='تم استلام الجهاز وفحصه مبدئياً'
            )
            
            db.session.add(repair_ticket)
            db.session.commit()
            
            print(f"✅ Repair ticket created successfully!")
            print(f"   Ticket ID: {repair_ticket.id}")
            print(f"   Issue: {repair_ticket.issue_description}")
            print(f"   Priority: {repair_ticket.priority}")
            print()
            
            print("📱 Testing Telegram Notification System...")
            
            # Test Telegram notifications
            telegram_service = TelegramService()
            
            # Test connection
            connection_result = telegram_service.test_connection()
            
            if connection_result.get('success'):
                print("✅ Telegram bot connection successful!")
                bot_info = connection_result.get('bot_info', {})
                print(f"   Bot Name: {bot_info.get('first_name', 'Unknown')}")
                print(f"   Bot Username: @{bot_info.get('username', 'unknown')}")
                print()
                
                # Prepare notification data
                notification_data = {
                    "device_type": new_device.type,
                    "model": new_device.model,
                    "color": new_device.color,
                    "serial_number": new_device.serial_number,
                    "password": new_device.password,
                    "customer_name": new_device.owner_name,
                    "customer_phone": "+966501234567",
                    "customer_contact": f"{new_device.owner_name} (+966501234567)",
                    "technician_name": "غير مخصص",
                    "technician_contact": "غير مخصص",
                    "issue": repair_ticket.issue_description,
                    "notes": new_device.notes,
                    "device_id": new_device.id,
                    "reference_number": f"DEV-{new_device.id:06d}",
                    "date": new_device.created_at.strftime("%Y-%m-%d"),
                    "timestamp": new_device.created_at.strftime("%Y-%m-%d %H:%M:%S"),
                    "status": "مستلم",
                    "estimated_cost": "في انتظار التقدير",
                    "cost_status": "في انتظار التقدير",
                    "payment_status": "في الانتظار"
                }
                
                print("📤 Sending device added notification...")
                
                # Use the admin chat ID from config
                admin_chat_id = "5632855284"  # From telegram_config.env
                
                result = telegram_service.send_notification(
                    "device_added",
                    notification_data,
                    [admin_chat_id],
                    language='ar'
                )
                
                if result.get('success'):
                    print("✅ Telegram notification sent successfully!")
                    print(f"   Messages sent: {result.get('total_sent', 0)}")
                    print(f"   Messages failed: {result.get('total_failed', 0)}")
                else:
                    print("❌ Telegram notification failed")
                    print(f"   Error: {result.get('error', 'Unknown error')}")
                
            else:
                print("❌ Telegram bot connection failed")
                print(f"   Error: {connection_result.get('error', 'Unknown error')}")
                print("   Note: This might be due to missing bot token or network issues")
            
            print()
            print("=" * 60)
            print("📊 DEMO SUMMARY")
            print("=" * 60)
            print(f"✅ Device Created: {new_device.type} {new_device.model}")
            print(f"✅ Repair Ticket: #{repair_ticket.id}")
            print(f"✅ Database: Working")
            print(f"✅ Telegram: {'Working' if connection_result.get('success') else 'Configuration needed'}")
            print()
            print("🌐 Application URLs:")
            print("   Main App: http://127.0.0.1:5000")
            print("   Admin Tool: http://127.0.0.1:8081")
            print()
            print("📱 Device Details:")
            print(f"   Reference: DEV-{new_device.id:06d}")
            print(f"   Owner: {new_device.owner_name}")
            print(f"   Phone: +966501234567")
            print(f"   Issue: Screen damage and battery problems")
            print("=" * 60)
            
            return True
            
    except Exception as e:
        print(f"❌ Demo failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    demo_device_creation()
