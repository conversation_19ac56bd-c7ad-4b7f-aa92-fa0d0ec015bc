{% extends "base.html" %}

{% block title %}{{ _('إدارة قطع الغيار') }} - {{ _('Spare Parts Inventory') }}{% endblock %}

{% block extra_css %}
<!-- Enhanced Inventory Management Styles with Arabic Support -->
<style>
/* ===== ENHANCED INVENTORY MANAGEMENT SYSTEM ===== */
/* Modern, Professional Design with Arabic RTL Support */

/* Root Variables for Consistent Theming */
:root {
    --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --success-gradient: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
    --warning-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    --danger-gradient: linear-gradient(135deg, #fc466b 0%, #3f5efb 100%);
    --info-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);

    --card-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    --card-shadow-hover: 0 16px 48px rgba(0, 0, 0, 0.15);
    --border-radius: 16px;
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

    /* Arabic Font Support */
    --arabic-font: 'Segoe UI', 'Tahoma', 'Arial', 'Helvetica Neue', sans-serif;
    --arabic-font-weight: 500;
}

/* Arabic RTL Support */
[dir="rtl"] {
    font-family: var(--arabic-font);
    font-weight: var(--arabic-font-weight);
}

/* Enhanced Dashboard Header */
.dashboard-header {
    background: var(--primary-gradient);
    color: white;
    padding: 3rem 0;
    margin-bottom: 2.5rem;
    border-radius: 0 0 30px 30px;
    box-shadow: var(--card-shadow);
    position: relative;
    overflow: hidden;
}

.dashboard-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 30% 20%, rgba(255,255,255,0.1) 0%, transparent 50%),
                radial-gradient(circle at 70% 80%, rgba(255,255,255,0.08) 0%, transparent 50%);
    pointer-events: none;
}

.page-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    text-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.page-subtitle {
    font-size: 1.1rem;
    opacity: 0.9;
    margin-bottom: 0;
}

/* Enhanced Search Section */
.search-section {
    background: white;
    border-radius: var(--border-radius);
    padding: 2.5rem;
    box-shadow: var(--card-shadow);
    margin-bottom: 2.5rem;
    border: 1px solid rgba(0,0,0,0.05);
    position: relative;
    backdrop-filter: blur(10px);
}

.search-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--primary-gradient);
    border-radius: var(--border-radius) var(--border-radius) 0 0;
}

/* Enhanced Statistics Grid */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
    margin-bottom: 3rem;
}

.stats-card {
    background: white;
    border-radius: var(--border-radius);
    padding: 2.5rem;
    text-align: center;
    box-shadow: var(--card-shadow);
    transition: var(--transition);
    position: relative;
    overflow: hidden;
    border: 1px solid rgba(0,0,0,0.05);
}

.stats-card::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -50%;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
    transform: rotate(45deg);
    pointer-events: none;
}

.stats-card:hover {
    transform: translateY(-12px) scale(1.02);
    box-shadow: var(--card-shadow-hover);
}

.stats-card.total {
    background: linear-gradient(135deg, #ffffff 0%, #f0f7ff 100%);
    border-left: 6px solid #007bff;
}
.stats-card.low-stock {
    background: linear-gradient(135deg, #ffffff 0%, #fffbf0 100%);
    border-left: 6px solid #ffc107;
}
.stats-card.out-stock {
    background: linear-gradient(135deg, #ffffff 0%, #fff0f0 100%);
    border-left: 6px solid #dc3545;
}
.stats-card.value {
    background: linear-gradient(135deg, #ffffff 0%, #f0fff0 100%);
    border-left: 6px solid #28a745;
}

.stats-number {
    font-size: 3rem;
    font-weight: 800;
    margin-bottom: 0.5rem;
    color: #2c3e50;
    text-shadow: 0 2px 4px rgba(0,0,0,0.1);
    position: relative;
    z-index: 2;
}

.stats-label {
    color: #6c757d;
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 1.5px;
    font-weight: 600;
    position: relative;
    z-index: 2;
}

.stats-icon {
    position: absolute;
    top: 1.5rem;
    right: 1.5rem;
    font-size: 2rem;
    opacity: 0.2;
    z-index: 1;
}

/* Enhanced Parts Grid Layout */
.parts-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 2rem;
    margin-bottom: 3rem;
}

/* Modern Part Card Design */
.part-card {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--card-shadow);
    transition: var(--transition);
    overflow: hidden;
    border: 1px solid rgba(0,0,0,0.05);
    position: relative;
}

.part-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--card-shadow-hover);
}

.part-card.low-stock {
    border-left: 6px solid #ffc107;
    background: linear-gradient(135deg, #ffffff 0%, #fffbf0 100%);
}

.part-card.out-of-stock {
    border-left: 6px solid #dc3545;
    background: linear-gradient(135deg, #ffffff 0%, #fff0f0 100%);
}

.part-card.in-stock {
    border-left: 6px solid #28a745;
}

/* Card Header */
.card-header {
    padding: 1.5rem;
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
    border-bottom: 1px solid rgba(0,0,0,0.05);
    position: relative;
}

.stock-status-indicator {
    position: absolute;
    top: 1rem;
    right: 1rem;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    box-shadow: 0 0 0 3px rgba(255,255,255,0.8);
}

.stock-status-indicator.in-stock {
    background: #28a745;
    box-shadow: 0 0 0 3px rgba(40, 167, 69, 0.2);
}

.stock-status-indicator.low-stock {
    background: #ffc107;
    box-shadow: 0 0 0 3px rgba(255, 193, 7, 0.2);
    animation: pulse 2s infinite;
}

.stock-status-indicator.out-of-stock {
    background: #dc3545;
    box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.2);
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); opacity: 1; }
    50% { transform: scale(1.2); opacity: 0.7; }
    100% { transform: scale(1); opacity: 1; }
}

.part-name {
    font-size: 1.25rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 0.5rem;
    line-height: 1.3;
}

.part-number {
    font-size: 0.9rem;
    color: #6c757d;
    font-family: 'Courier New', monospace;
    background: rgba(0,0,0,0.05);
    padding: 0.25rem 0.5rem;
    border-radius: 6px;
    display: inline-block;
}

/* Card Body */
.card-body {
    padding: 1.5rem;
}

.part-details {
    margin-bottom: 1.5rem;
}

.detail-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.75rem;
    padding: 0.5rem 0;
    border-bottom: 1px solid rgba(0,0,0,0.05);
}

.detail-row:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.detail-label {
    font-size: 0.85rem;
    color: #6c757d;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.detail-value {
    font-size: 0.95rem;
    color: #2c3e50;
    font-weight: 500;
}

/* Stock Information */
.stock-info {
    text-align: center;
    padding: 1.5rem;
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
    border-radius: 12px;
    margin-bottom: 1.5rem;
    border: 1px solid rgba(0,0,0,0.05);
}

.stock-count {
    font-size: 2.5rem;
    font-weight: 800;
    color: #2c3e50;
    margin-bottom: 0.25rem;
    text-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.stock-label {
    font-size: 0.8rem;
    color: #6c757d;
    text-transform: uppercase;
    letter-spacing: 1px;
    font-weight: 600;
}

/* Price Section */
.price-section {
    text-align: center;
    margin-bottom: 1.5rem;
}

.price-display {
    font-size: 1.75rem;
    font-weight: 700;
    color: #28a745;
    text-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

/* Enhanced Filter Section */
.filter-section {
    background: white;
    border-radius: var(--border-radius);
    padding: 2.5rem;
    box-shadow: var(--card-shadow);
    margin-bottom: 2.5rem;
    border: 1px solid rgba(0,0,0,0.05);
    position: relative;
}

.filter-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--info-gradient);
    border-radius: var(--border-radius) var(--border-radius) 0 0;
}

.filter-row {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr 1fr auto;
    gap: 2rem;
    align-items: end;
}

@media (max-width: 768px) {
    .filter-row {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
}

.form-select, .form-control {
    border: 2px solid #e9ecef;
    border-radius: 12px;
    padding: 0.875rem 1rem;
    font-size: 0.95rem;
    transition: var(--transition);
    background: rgba(255,255,255,0.9);
    backdrop-filter: blur(10px);
}

.form-select:focus, .form-control:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.25rem rgba(102, 126, 234, 0.25);
    transform: translateY(-2px);
}

/* Enhanced Action Buttons */
.action-buttons {
    padding: 1.5rem;
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
    border-top: 1px solid rgba(0,0,0,0.05);
}

.primary-action {
    margin-bottom: 1rem;
}

.main-action-btn {
    display: block;
    width: 100%;
    padding: 0.875rem 1.5rem;
    background: var(--primary-gradient);
    color: white;
    text-decoration: none;
    border-radius: 12px;
    font-weight: 600;
    text-align: center;
    transition: var(--transition);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.main-action-btn:hover {
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(102, 126, 234, 0.4);
}

.secondary-actions {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 0.75rem;
}

.btn-action {
    padding: 0.75rem;
    border: none;
    border-radius: 10px;
    font-size: 0.9rem;
    font-weight: 600;
    transition: var(--transition);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    min-height: 44px;
}

.btn-action:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.btn-action.btn-secondary {
    background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
    color: white;
}

.btn-action.btn-success {
    background: var(--success-gradient);
    color: white;
}

.btn-action.btn-warning {
    background: var(--warning-gradient);
    color: white;
}

.btn-action.btn-danger {
    background: var(--danger-gradient);
    color: white;
}

.btn-action.btn-outline {
    background: white;
    color: #6c757d;
    border: 2px solid #e9ecef;
}

.btn-action:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none !important;
}

/* Enhanced Empty State */
.empty-state {
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
    border-radius: var(--border-radius);
    padding: 4rem 2rem;
    text-align: center;
    border: 2px dashed #dee2e6;
    margin: 3rem auto;
    max-width: 600px;
    box-shadow: var(--card-shadow);
}

.empty-state i {
    font-size: 4rem;
    color: #adb5bd;
    margin-bottom: 1.5rem;
    display: block;
}

.empty-state h3 {
    color: #495057;
    margin-bottom: 1rem;
    font-weight: 700;
}

.empty-state p {
    color: #6c757d;
    margin-bottom: 2rem;
    font-size: 1rem;
    line-height: 1.6;
}

/* Responsive Design */
@media (max-width: 768px) {
    .parts-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 1rem;
    }

    .secondary-actions {
        grid-template-columns: repeat(2, 1fr);
    }

    .page-title {
        font-size: 2rem;
    }
}

@media (max-width: 480px) {
    .stats-grid {
        grid-template-columns: 1fr;
    }

    .dashboard-header {
        padding: 2rem 0;
    }

    .search-section, .filter-section {
        padding: 1.5rem;
    }
}

/* Arabic RTL Specific Styles */
[dir="rtl"] .part-number {
    direction: ltr;
    text-align: right;
}

[dir="rtl"] .stock-status-indicator {
    right: auto;
    left: 1rem;
}

[dir="rtl"] .stats-icon {
    right: auto;
    left: 1.5rem;
}

[dir="rtl"] .detail-row {
    flex-direction: row-reverse;
}

/* Loading Animation */
@keyframes shimmer {
    0% { background-position: -200px 0; }
    100% { background-position: calc(200px + 100%) 0; }
}

.loading-shimmer {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200px 100%;
    animation: shimmer 1.5s infinite;
}

</style>
{% endblock %}

{% block content %}
<div class="container-fluid" dir="rtl">
    <!-- Enhanced Professional Header -->
    <div class="dashboard-header">
        <div class="container-fluid">
            <div class="row">
                <div class="col-12">
                    <div class="d-flex justify-content-between align-items-center flex-wrap">
                        <div class="mb-3 mb-md-0">
                            <h1 class="page-title">
                                <i class="bi bi-boxes me-3"></i>
                                {{ _('إدارة قطع الغيار') }}
                                <small class="d-block mt-1" style="font-size: 0.6em; opacity: 0.8;">
                                    {{ _('Spare Parts Inventory Management') }}
                                </small>
                            </h1>
                            <p class="page-subtitle">
                                {{ _('إدارة مخزون قطع غيار الهواتف بكفاءة عالية') }}
                                <br>
                                <small style="opacity: 0.8;">{{ _('Manage phone spare parts and inventory efficiently') }}</small>
                            </p>
                        </div>
                        <div class="d-flex gap-2 flex-wrap">
                            <a href="{{ url_for('spare_parts.sales_list') }}" class="btn btn-light">
                                <i class="bi bi-graph-up"></i>
                                <span class="d-none d-sm-inline">{{ _('تقرير المبيعات') }}</span>
                                <span class="d-sm-none">{{ _('Sales') }}</span>
                            </a>
                            <a href="{{ url_for('spare_parts.add_part') }}" class="btn btn-warning">
                                <i class="bi bi-plus-circle"></i>
                                <span class="d-none d-sm-inline">{{ _('إضافة قطعة جديدة') }}</span>
                                <span class="d-sm-none">{{ _('Add') }}</span>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Enhanced Search Section -->
    <div class="search-section">
        <form method="GET" action="{{ url_for('spare_parts.index') }}">
            <div class="row">
                <div class="col-md-8 mb-3">
                    <label class="form-label fw-bold">
                        <i class="bi bi-search me-2"></i>
                        {{ _('البحث في قطع الغيار') }} / {{ _('Search Parts') }}
                    </label>
                    <div class="input-group">
                        <span class="input-group-text bg-light border-0">
                            <i class="bi bi-search text-muted"></i>
                        </span>
                        <input type="text" class="form-control search-input border-start-0"
                               name="search"
                               placeholder="{{ _('البحث بالاسم، رقم القطعة، أو العلامة التجارية...') }}"
                               value="{{ search or '' }}"
                               dir="auto">
                    </div>
                    <small class="form-text text-muted">
                        {{ _('Search by name, part number, brand, or description') }}
                    </small>
                </div>
                <div class="col-md-4 mb-3">
                    <label class="form-label fw-bold">
                        <i class="bi bi-lightning me-2"></i>
                        {{ _('إجراءات سريعة') }} / {{ _('Quick Actions') }}
                    </label>
                    <div class="d-flex gap-2">
                        <button type="submit" class="btn btn-primary flex-fill">
                            <i class="bi bi-search"></i> {{ _('بحث') }}
                        </button>
                        <a href="{{ url_for('spare_parts.index') }}" class="btn btn-outline-secondary flex-fill">
                            <i class="bi bi-arrow-clockwise"></i> {{ _('مسح') }}
                        </a>
                    </div>
                </div>
            </div>
        </form>
    </div>

    <!-- Enhanced Statistics Grid with Icons -->
    <div class="stats-grid">
        <div class="stats-card total">
            <i class="bi bi-boxes stats-icon"></i>
            <div class="stats-number">{{ stats.total_parts }}</div>
            <div class="stats-label">
                {{ _('إجمالي القطع') }}
                <br><small>{{ _('Total Parts') }}</small>
            </div>
        </div>
        <div class="stats-card low-stock">
            <i class="bi bi-exclamation-triangle stats-icon"></i>
            <div class="stats-number">{{ stats.low_stock_count }}</div>
            <div class="stats-label">
                {{ _('مخزون منخفض') }}
                <br><small>{{ _('Low Stock') }}</small>
            </div>
        </div>
        <div class="stats-card out-stock">
            <i class="bi bi-x-circle stats-icon"></i>
            <div class="stats-number">{{ stats.out_of_stock_count }}</div>
            <div class="stats-label">
                {{ _('نفد المخزون') }}
                <br><small>{{ _('Out of Stock') }}</small>
            </div>
        </div>
        <div class="stats-card value">
            <i class="bi bi-currency-dollar stats-icon"></i>
            <div class="stats-number">${{ "%.0f"|format(stats.total_value) }}</div>
            <div class="stats-label">
                {{ _('القيمة الإجمالية') }}
                <br><small>{{ _('Total Value') }}</small>
            </div>
        </div>
    </div>

    <!-- Enhanced Advanced Filters -->
    <div class="filter-section">
        <h5 class="mb-4">
            <i class="bi bi-funnel me-2"></i>
            {{ _('فلاتر متقدمة') }} / {{ _('Advanced Filters') }}
        </h5>
        <form method="GET" action="{{ url_for('spare_parts.index') }}">
            <input type="hidden" name="search" value="{{ search or '' }}">
            <div class="filter-row">
                <div>
                    <label class="form-label fw-bold">
                        <i class="bi bi-tags me-1"></i>
                        {{ _('الفئة') }} / {{ _('Category') }}
                    </label>
                    <select class="form-select" name="category">
                        <option value="">{{ _('جميع الفئات') }} / {{ _('All Categories') }}</option>
                        {% for cat in categories %}
                        <option value="{{ cat }}" {% if cat == selected_category %}selected{% endif %}>
                            {{ cat }}
                        </option>
                        {% endfor %}
                    </select>
                </div>
                <div>
                    <label class="form-label fw-bold">
                        <i class="bi bi-award me-1"></i>
                        {{ _('العلامة التجارية') }} / {{ _('Brand') }}
                    </label>
                    <select class="form-select" name="brand">
                        <option value="">{{ _('جميع العلامات') }} / {{ _('All Brands') }}</option>
                        {% for brand in brands %}
                        <option value="{{ brand }}" {% if brand == selected_brand %}selected{% endif %}>
                            {{ brand }}
                        </option>
                        {% endfor %}
                    </select>
                </div>
                <div>
                    <label class="form-label fw-bold">
                        <i class="bi bi-clipboard-data me-1"></i>
                        {{ _('حالة المخزون') }} / {{ _('Stock Status') }}
                    </label>
                    <select class="form-select" name="stock_status">
                        <option value="">{{ _('جميع الحالات') }} / {{ _('All Status') }}</option>
                        <option value="in_stock" {% if selected_stock_status == 'in_stock' %}selected{% endif %}>
                            <span>✅</span> {{ _('متوفر') }} / {{ _('In Stock') }}
                        </option>
                        <option value="low" {% if selected_stock_status == 'low' %}selected{% endif %}>
                            <span>⚠️</span> {{ _('مخزون منخفض') }} / {{ _('Low Stock') }}
                        </option>
                        <option value="out" {% if selected_stock_status == 'out' %}selected{% endif %}>
                            <span>❌</span> {{ _('نفد المخزون') }} / {{ _('Out of Stock') }}
                        </option>
                    </select>
                </div>
                <div>
                    <button type="submit" class="btn btn-primary">
                        <i class="bi bi-funnel"></i> {{ _('تطبيق') }}
                    </button>
                </div>
            </div>
        </form>
    </div>

    <!-- Enhanced Professional Parts Grid -->
    {% if parts.items %}
    <div class="parts-grid">
        {% for part in parts.items %}
        <div class="part-card
            {% if part.quantity_in_stock <= 0 %}out-of-stock{% elif part.quantity_in_stock <= part.minimum_stock_level %}low-stock{% else %}in-stock{% endif %}">

            <!-- Enhanced Card Header -->
            <div class="card-header">
                <!-- Color-coded Status Indicator with Arabic tooltips -->
                {% if part.quantity_in_stock <= 0 %}
                    <div class="stock-status-indicator out-of-stock"
                         title="{{ _('نفد المخزون') }} / {{ _('Out of Stock') }}"></div>
                {% elif part.quantity_in_stock <= part.minimum_stock_level %}
                    <div class="stock-status-indicator low-stock"
                         title="{{ _('مخزون منخفض') }} / {{ _('Low Stock') }}"></div>
                {% else %}
                    <div class="stock-status-indicator in-stock"
                         title="{{ _('متوفر') }} / {{ _('In Stock') }}"></div>
                {% endif %}

                <div class="part-name" dir="auto">{{ part.name }}</div>
                <div class="part-number">{{ part.part_number }}</div>
            </div>

            <!-- Enhanced Card Body -->
            <div class="card-body">
                <div class="part-details">
                    <div class="detail-row">
                        <span class="detail-label">
                            <i class="bi bi-tags me-1"></i>
                            {{ _('الفئة') }} / {{ _('Category') }}
                        </span>
                        <span class="detail-value" dir="auto">{{ part.category }}</span>
                    </div>
                    <div class="detail-row">
                        <span class="detail-label">
                            <i class="bi bi-award me-1"></i>
                            {{ _('العلامة') }} / {{ _('Brand') }}
                        </span>
                        <span class="detail-value" dir="auto">{{ part.brand }}</span>
                    </div>
                    {% if part.storage_location %}
                    <div class="detail-row">
                        <span class="detail-label">
                            <i class="bi bi-geo-alt me-1"></i>
                            {{ _('الموقع') }} / {{ _('Location') }}
                        </span>
                        <span class="detail-value" dir="auto">{{ part.storage_location }}</span>
                    </div>
                    {% endif %}
                    {% if part.supplier_name %}
                    <div class="detail-row">
                        <span class="detail-label">
                            <i class="bi bi-truck me-1"></i>
                            {{ _('المورد') }} / {{ _('Supplier') }}
                        </span>
                        <span class="detail-value" dir="auto">{{ part.supplier_name }}</span>
                    </div>
                    {% endif %}
                </div>

                <div class="stock-info">
                    <div class="stock-count">{{ part.quantity_in_stock }}</div>
                    <div class="stock-label">
                        {{ _('وحدة متاحة') }} / {{ _('Units Available') }}
                        {% if part.minimum_stock_level %}
                        <br><small class="text-muted">
                            {{ _('الحد الأدنى') }}: {{ part.minimum_stock_level }}
                        </small>
                        {% endif %}
                    </div>
                </div>

                <div class="price-section">
                    <div class="price-display">${{ "%.2f"|format(part.selling_price) }}</div>
                    {% if part.cost_price and part.cost_price > 0 %}
                    <small class="text-muted d-block">
                        {{ _('التكلفة') }}: ${{ "%.2f"|format(part.cost_price) }}
                        {% if part.selling_price > part.cost_price %}
                        <span class="text-success">
                            (+${{ "%.2f"|format(part.selling_price - part.cost_price) }})
                        </span>
                        {% endif %}
                    </small>
                    {% endif %}
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="action-buttons">
                <!-- Primary Action -->
                <div class="primary-action">
                    <a href="{{ url_for('spare_parts.view_part', part_id=part.id) }}"
                       class="main-action-btn">
                        <i class="bi bi-eye"></i> {{ _('View Details') }}
                    </a>
                </div>

                <!-- Secondary Actions Grid -->
                <div class="secondary-actions">
                    <a href="{{ url_for('spare_parts.edit_part', part_id=part.id) }}"
                       class="btn-action btn-secondary">
                        <i class="bi bi-pencil"></i> {{ _('Edit') }}
                    </a>
                    <button class="btn-action btn-success"
                            onclick="showSellModal({{ part.id }}, '{{ part.name }}', {{ part.quantity_in_stock }}, {{ part.selling_price }})"
                            {% if part.quantity_in_stock <= 0 %}disabled{% endif %}>
                        <i class="bi bi-cart-plus"></i>
                    </button>
                    <button class="btn-action btn-warning"
                            onclick="showStockModal({{ part.id }}, '{{ part.name }}', {{ part.quantity_in_stock }})">
                        <i class="bi bi-plus-minus"></i>
                    </button>
                    <button class="btn-action btn-outline"
                            onclick="showImageUploadModal({{ part.id }}, '{{ part.name }}')">
                        <i class="bi bi-image"></i>
                    </button>
                    <button class="btn-action btn-outline"
                            onclick="generateBarcode({{ part.id }})">
                        <i class="bi bi-upc-scan"></i>
                    </button>
                    <button class="btn-action btn-danger"
                            onclick="confirmDelete({{ part.id }}, '{{ part.name }}')">
                        <i class="bi bi-trash"></i>
                    </button>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>

    <!-- Pagination -->
    {% if parts.pages > 1 %}
    <div class="row">
        <div class="col-12">
            <nav aria-label="Parts pagination">
                <ul class="pagination justify-content-center">
                    {% if parts.has_prev %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('spare_parts.index', page=parts.prev_num, search=search, category=selected_category, brand=selected_brand, stock_status=selected_stock_status) }}">
                            {{ _('Previous') }}
                        </a>
                    </li>
                    {% endif %}
                    
                    {% for page_num in parts.iter_pages() %}
                        {% if page_num %}
                            {% if page_num != parts.page %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('spare_parts.index', page=page_num, search=search, category=selected_category, brand=selected_brand, stock_status=selected_stock_status) }}">
                                    {{ page_num }}
                                </a>
                            </li>
                            {% else %}
                            <li class="page-item active">
                                <span class="page-link">{{ page_num }}</span>
                            </li>
                            {% endif %}
                        {% else %}
                        <li class="page-item disabled">
                            <span class="page-link">...</span>
                        </li>
                        {% endif %}
                    {% endfor %}
                    
                    {% if parts.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('spare_parts.index', page=parts.next_num, search=search, category=selected_category, brand=selected_brand, stock_status=selected_stock_status) }}">
                            {{ _('Next') }}
                        </a>
                    </li>
                    {% endif %}
                </ul>
            </nav>
        </div>
    </div>
    {% endif %}

    {% else %}
    <!-- Professional Empty State -->
    <div class="empty-state">
        <i class="bi bi-box-seam"></i>
        <h3>{{ _('No spare parts found') }}</h3>
        <p>{{ _('Start by adding your first spare part to the inventory or adjust your search filters') }}</p>
        <div class="mt-4">
            <a href="{{ url_for('spare_parts.add_part') }}" class="btn btn-primary me-2">
                <i class="bi bi-plus-circle"></i> {{ _('Add First Part') }}
            </a>
            <a href="{{ url_for('spare_parts.index') }}" class="btn btn-outline-secondary">
                <i class="bi bi-arrow-clockwise"></i> {{ _('Clear Filters') }}
            </a>
        </div>
    </div>
    {% endif %}
</div>

<!-- Stock Adjustment Modal -->
<div class="modal fade" id="stockModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">{{ _('Adjust Stock') }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="stockForm" method="POST">
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">{{ _('Part') }}</label>
                        <input type="text" class="form-control" id="partName" readonly>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">{{ _('Current Stock') }}</label>
                        <input type="number" class="form-control" id="currentStock" readonly>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">{{ _('Adjustment Type') }}</label>
                        <select class="form-select" name="adjustment_type" required>
                            <option value="add">{{ _('Add Stock') }}</option>
                            <option value="remove">{{ _('Remove Stock') }}</option>
                            <option value="set">{{ _('Set Stock') }}</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">{{ _('Quantity') }}</label>
                        <input type="number" class="form-control" name="quantity" min="0" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">{{ _('Reason') }}</label>
                        <textarea class="form-control" name="reason" rows="3" required 
                                  placeholder="{{ _('Explain the reason for this adjustment...') }}"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        {{ _('Cancel') }}
                    </button>
                    <button type="submit" class="btn btn-primary">
                        {{ _('Adjust Stock') }}
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Sell Modal -->
<div class="modal fade" id="sellModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="bi bi-cart-plus text-success"></i>
                    {{ _('Sell Spare Part') }}
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="sellForm" method="POST">
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">{{ _('Part') }}</label>
                                <input type="text" class="form-control" id="sellPartName" readonly>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">{{ _('Available Stock') }}</label>
                                <input type="number" class="form-control" id="sellAvailableStock" readonly>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">{{ _('Unit Price') }}</label>
                                <div class="input-group">
                                    <span class="input-group-text">$</span>
                                    <input type="number" class="form-control" id="sellUnitPrice" readonly>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">{{ _('Quantity to Sell') }}</label>
                                <input type="number" class="form-control" name="quantity" id="sellQuantity"
                                       min="1" value="1" required onchange="calculateTotal()">
                            </div>
                            <div class="mb-3">
                                <label class="form-label">{{ _('Discount (%)') }}</label>
                                <input type="number" class="form-control" name="discount_percentage"
                                       id="sellDiscount" min="0" max="100" step="0.1" value="0" onchange="calculateTotal()">
                            </div>
                            <div class="mb-3">
                                <label class="form-label">{{ _('Payment Method') }}</label>
                                <select class="form-select" name="payment_method">
                                    <option value="cash">{{ _('Cash') }}</option>
                                    <option value="card">{{ _('Credit/Debit Card') }}</option>
                                    <option value="bank_transfer">{{ _('Bank Transfer') }}</option>
                                    <option value="mobile_payment">{{ _('Mobile Payment') }}</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <hr>

                    <div class="row">
                        <div class="col-md-6">
                            <h6>{{ _('Customer Information') }}</h6>
                            <div class="mb-3">
                                <label class="form-label">{{ _('Customer Name') }}</label>
                                <input type="text" class="form-control" name="customer_name"
                                       placeholder="{{ _('Optional - for receipt') }}">
                            </div>
                            <div class="mb-3">
                                <label class="form-label">{{ _('Phone Number') }}</label>
                                <input type="tel" class="form-control" name="customer_phone"
                                       placeholder="{{ _('Optional') }}">
                            </div>
                            <div class="mb-3">
                                <label class="form-label">{{ _('Email') }}</label>
                                <input type="email" class="form-control" name="customer_email"
                                       placeholder="{{ _('Optional') }}">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <h6>{{ _('Sale Summary') }}</h6>
                            <div class="card bg-light">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <span>{{ _('Subtotal:') }}</span>
                                        <span id="sellSubtotal">$0.00</span>
                                    </div>
                                    <div class="d-flex justify-content-between">
                                        <span>{{ _('Discount:') }}</span>
                                        <span id="sellDiscountAmount">$0.00</span>
                                    </div>
                                    <hr>
                                    <div class="d-flex justify-content-between fw-bold">
                                        <span>{{ _('Total:') }}</span>
                                        <span id="sellTotal">$0.00</span>
                                    </div>
                                </div>
                            </div>
                            <div class="mb-3 mt-3">
                                <label class="form-label">{{ _('Notes') }}</label>
                                <textarea class="form-control" name="notes" rows="3"
                                          placeholder="{{ _('Optional sale notes...') }}"></textarea>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        {{ _('Cancel') }}
                    </button>
                    <button type="submit" class="btn btn-success">
                        <i class="bi bi-cart-check"></i> {{ _('Complete Sale') }}
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="bi bi-exclamation-triangle text-danger"></i>
                    {{ _('Confirm Delete') }}
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>{{ _('Are you sure you want to delete this spare part?') }}</p>
                <div class="alert alert-warning">
                    <strong id="deletePartName"></strong>
                </div>
                <p class="text-muted small">
                    {{ _('Note: If this part has sales history, it will be deactivated instead of permanently deleted.') }}
                </p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    {{ _('Cancel') }}
                </button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    <button type="submit" class="btn btn-danger">
                        <i class="bi bi-trash"></i> {{ _('Delete') }}
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Image Upload Modal -->
<div class="modal fade" id="imageUploadModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="bi bi-image text-info"></i>
                    {{ _('Upload Image') }}
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="imageUploadForm" method="POST" enctype="multipart/form-data">
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">{{ _('Part Name') }}</label>
                        <input type="text" class="form-control" id="imagePartName" readonly>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">{{ _('Select Image') }}</label>
                        <input type="file" class="form-control" name="image" accept="image/*" required>
                        <div class="form-text">{{ _('Supported formats: JPG, PNG, GIF. Max size: 5MB') }}</div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        {{ _('Cancel') }}
                    </button>
                    <button type="submit" class="btn btn-info">
                        <i class="bi bi-upload"></i> {{ _('Upload') }}
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- JavaScript for inventory card enhancements -->
<script src="{{ url_for('static', filename='js/inventory_cards.js') }}"></script>

<script>
    // Original JavaScript in the page
    function showStockModal(partId, partName, currentStock) {
        document.getElementById('partName').value = partName;
        document.getElementById('currentStock').value = currentStock;
        document.getElementById('stockForm').action = '/spare-parts/' + partId + '/adjust_stock';
        
        const stockModal = new bootstrap.Modal(document.getElementById('stockModal'));
        stockModal.show();
    }
    
    function showSellModal(partId, partName, availableStock, unitPrice) {
        document.getElementById('sellPartName').value = partName;
        document.getElementById('sellAvailableStock').value = availableStock;
        document.getElementById('sellUnitPrice').value = unitPrice;
        document.getElementById('sellForm').action = '/spare-parts/' + partId + '/sell';
        
        calculateTotal();
        
        const sellModal = new bootstrap.Modal(document.getElementById('sellModal'));
        sellModal.show();
    }
    
    function calculateTotal() {
        const quantity = document.getElementById('sellQuantity').value;
        const unitPrice = document.getElementById('sellUnitPrice').value;
        const discountPercentage = document.getElementById('sellDiscount').value;
        
        const subtotal = quantity * unitPrice;
        const discountAmount = (subtotal * discountPercentage) / 100;
        const total = subtotal - discountAmount;
        
        document.getElementById('sellSubtotal').textContent = '$' + subtotal.toFixed(2);
        document.getElementById('sellDiscountAmount').textContent = '$' + discountAmount.toFixed(2);
        document.getElementById('sellTotal').textContent = '$' + total.toFixed(2);
    }
    
    function confirmDelete(partId, partName) {
        if (confirm('{{ _("Are you sure you want to delete the part:") }} ' + partName + '?')) {
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = '/spare-parts/' + partId + '/delete';
            document.body.appendChild(form);
            form.submit();
        }
    }
    
    function showImageUploadModal(partId, partName) {
        document.getElementById('uploadPartId').value = partId;
        document.getElementById('uploadPartName').textContent = partName;
        document.getElementById('uploadForm').action = '/spare-parts/' + partId + '/upload_image';
        
        const uploadModal = new bootstrap.Modal(document.getElementById('imageUploadModal'));
        uploadModal.show();
    }
    
    function generateBarcode(partId) {
        window.open('/spare-parts/' + partId + '/generate_barcode', '_blank');
    }
    
    // تطبيق تصميم البطاقات
    document.addEventListener('DOMContentLoaded', function() {
        console.log('Applying card design for inventory items');
    });

    // Make sure global functions are accessible to our updated JS
    window.showStockModal = showStockModal;
    window.showSellModal = showSellModal;
    window.confirmDelete = confirmDelete;
    window.showImageUploadModal = showImageUploadModal;
    window.generateBarcode = generateBarcode;
</script>
{% endblock %}
