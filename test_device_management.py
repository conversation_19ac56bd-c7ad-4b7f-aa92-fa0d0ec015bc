#!/usr/bin/env python3
"""
Test Device Management System
Demonstrates adding devices and testing Telegram notifications
"""

import sys
import os
import requests
import json
from datetime import datetime

# Add the app directory to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_application_status():
    """Test if the application is running"""
    print("🔍 Testing Application Status...")
    try:
        response = requests.get('http://127.0.0.1:5000/', timeout=10)
        if response.status_code == 200:
            print("✅ Main application is running successfully")
            print(f"   Status Code: {response.status_code}")
            print(f"   Content Length: {len(response.text)} characters")
            return True
        else:
            print(f"❌ Application returned status code: {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"❌ Failed to connect to application: {e}")
        return False

def test_admin_tool():
    """Test if the admin tool is running"""
    print("\n🔧 Testing Admin Tool Status...")
    try:
        response = requests.get('http://127.0.0.1:8081/', timeout=10)
        if response.status_code == 200:
            print("✅ Admin tool is running successfully")
            print(f"   Status Code: {response.status_code}")
            return True
        else:
            print(f"❌ Admin tool returned status code: {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"❌ Failed to connect to admin tool: {e}")
        return False

def test_database_connection():
    """Test database connection by adding a device directly"""
    print("\n💾 Testing Database Connection...")
    try:
        from app import create_app, db
        from app.models.device import Device
        from app.models.user import User
        
        app = create_app()
        
        with app.app_context():
            # Test database connection
            device_count = Device.query.count()
            user_count = User.query.count()
            
            print(f"✅ Database connection successful")
            print(f"   Current devices in database: {device_count}")
            print(f"   Current users in database: {user_count}")
            
            return True
            
    except Exception as e:
        print(f"❌ Database connection failed: {e}")
        return False

def create_test_device():
    """Create a test device to demonstrate the system"""
    print("\n📱 Creating Test Device...")
    try:
        from app import create_app, db
        from app.models.device import Device
        from app.models.repair_ticket import RepairTicket
        
        app = create_app()
        
        with app.app_context():
            # Create a test device
            test_device = Device(
                type='iPhone',
                model='14 Pro Max',
                color='Deep Purple',
                serial_number='TEST123456789',
                password='محمي',
                owner_name='أحمد محمد العلي',
                status='receiving',
                tenant_id=1
            )
            
            # Add phone number in notes
            test_device.notes = "Owner Phone: +966501234567"
            
            db.session.add(test_device)
            db.session.flush()
            
            print(f"✅ Test device created successfully")
            print(f"   Device ID: {test_device.id}")
            print(f"   Type: {test_device.type} {test_device.model}")
            print(f"   Owner: {test_device.owner_name}")
            print(f"   Status: {test_device.status}")
            
            # Create a repair ticket for the device
            repair_ticket = RepairTicket(
                device_id=test_device.id,
                issue_description='الشاشة مكسورة ولا تستجيب للمس، البطارية تفرغ بسرعة',
                status='received',
                priority='normal'
            )
            
            db.session.add(repair_ticket)
            db.session.commit()
            
            print(f"✅ Repair ticket created successfully")
            print(f"   Ticket ID: {repair_ticket.id}")
            print(f"   Issue: {repair_ticket.issue_description}")
            
            return test_device, repair_ticket
            
    except Exception as e:
        print(f"❌ Failed to create test device: {e}")
        return None, None

def test_telegram_notifications(device, ticket):
    """Test Telegram notification system"""
    print("\n📱 Testing Telegram Notifications...")
    try:
        from app import create_app
        from app.services.telegram_service import TelegramService
        
        app = create_app()
        
        with app.app_context():
            telegram_service = TelegramService()
            
            # Test connection first
            print("🔍 Testing Telegram bot connection...")
            connection_result = telegram_service.test_connection()
            
            if connection_result.get('success'):
                print("✅ Telegram bot connection successful")
                bot_info = connection_result.get('bot_info', {})
                print(f"   Bot Name: {bot_info.get('first_name', 'Unknown')}")
                print(f"   Bot Username: @{bot_info.get('username', 'unknown')}")
            else:
                print("❌ Telegram bot connection failed")
                print(f"   Error: {connection_result.get('error', 'Unknown error')}")
                return False
            
            # Prepare notification data
            notification_data = {
                "device_type": device.type,
                "model": device.model,
                "color": device.color,
                "serial_number": device.serial_number,
                "password": device.password,
                "customer_name": device.owner_name,
                "customer_phone": "+966501234567",
                "customer_contact": f"{device.owner_name} (+966501234567)",
                "technician_name": "غير مخصص",
                "technician_contact": "غير مخصص",
                "issue": ticket.issue_description if ticket else "غير محددة",
                "notes": device.notes or "لا توجد ملاحظات إضافية",
                "device_id": device.id,
                "reference_number": f"DEV-{device.id:06d}",
                "date": device.created_at.strftime("%Y-%m-%d"),
                "timestamp": device.created_at.strftime("%Y-%m-%d %H:%M:%S"),
                "status": "مستلم",
                "estimated_cost": "في انتظار التقدير",
                "cost_status": "في انتظار التقدير",
                "payment_status": "في الانتظار"
            }
            
            print("📤 Sending device added notification...")
            
            # For testing, we'll use a test chat ID (you can replace with actual admin chat ID)
            test_recipients = ["5632855284"]  # This is from the config
            
            result = telegram_service.send_notification(
                "device_added", 
                notification_data, 
                test_recipients,
                language='ar'
            )
            
            if result.get('success'):
                print("✅ Telegram notification sent successfully")
                print(f"   Total sent: {result.get('total_sent', 0)}")
                print(f"   Total failed: {result.get('total_failed', 0)}")
            else:
                print("❌ Telegram notification failed")
                print(f"   Error: {result.get('error', 'Unknown error')}")
            
            return result.get('success', False)
            
    except Exception as e:
        print(f"❌ Telegram notification test failed: {e}")
        return False

def main():
    """Main test function"""
    print("🚀 DEVICE REPAIR MANAGEMENT SYSTEM - FUNCTIONALITY TEST")
    print("=" * 70)
    print(f"⏰ Test started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # Test 1: Application Status
    app_running = test_application_status()
    
    # Test 2: Admin Tool Status
    admin_running = test_admin_tool()
    
    # Test 3: Database Connection
    db_working = test_database_connection()
    
    if not (app_running and db_working):
        print("\n❌ Basic system tests failed. Please check the application setup.")
        return
    
    # Test 4: Create Test Device
    device, ticket = create_test_device()
    
    if not device:
        print("\n❌ Device creation failed. Cannot proceed with notification test.")
        return
    
    # Test 5: Test Telegram Notifications
    telegram_working = test_telegram_notifications(device, ticket)
    
    # Summary
    print("\n" + "=" * 70)
    print("📊 TEST SUMMARY")
    print("=" * 70)
    print(f"✅ Main Application: {'WORKING' if app_running else 'FAILED'}")
    print(f"✅ Admin Tool: {'WORKING' if admin_running else 'FAILED'}")
    print(f"✅ Database: {'WORKING' if db_working else 'FAILED'}")
    print(f"✅ Device Creation: {'WORKING' if device else 'FAILED'}")
    print(f"✅ Telegram Notifications: {'WORKING' if telegram_working else 'FAILED'}")
    print()
    
    if all([app_running, db_working, device, telegram_working]):
        print("🎉 ALL TESTS PASSED! The system is working correctly.")
        print()
        print("🌐 Access the application at: http://127.0.0.1:5000")
        print("🔧 Access the admin tool at: http://127.0.0.1:8081")
    else:
        print("⚠️ Some tests failed. Please check the issues above.")
    
    print("=" * 70)

if __name__ == "__main__":
    main()
